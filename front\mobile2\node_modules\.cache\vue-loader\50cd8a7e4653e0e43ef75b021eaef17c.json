{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue?vue&type=style&index=0&id=1dcb2485&prod&lang=less&scoped=true", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue", "mtime": 1753489704677}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751130694554}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751130710731}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751130698336}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1751130697755}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\style-resources-loader\\lib\\index.js", "mtime": 1751130698073}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751130703090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}