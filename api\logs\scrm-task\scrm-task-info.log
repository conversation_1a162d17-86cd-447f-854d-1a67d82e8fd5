2025-07-26 09:02:32.674 [main] INFO  o.scrm.scheduler.ScrmTaskApplication - The following 1 profile is active: "dev"
2025-07-26 09:02:32.706 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:02:32.707 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:02:32.707 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:02:32.707 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:02:32.707 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:02:32.707 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:02:32.707 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:02:35.165 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:02:35.169 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:02:35.200 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-07-26 09:02:35.778 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=827235e6-fd6d-3295-9b20-ec7c3f41506a
2025-07-26 09:02:36.417 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$9c03d8d6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:36.465 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:36.483 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:36.487 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:36.495 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:36.508 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:36.512 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:36.512 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$532/371637727] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:36.529 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:36.549 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:36.622 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:36.648 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:36.690 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm.scheduler]'.
2025-07-26 09:02:37.360 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6400 (http)
2025-07-26 09:02:37.376 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6400"]
2025-07-26 09:02:37.376 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 09:02:37.377 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 09:02:37.552 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-26 09:02:37.553 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4846 ms
2025-07-26 09:02:38.505 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:02:40.012 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-26 09:02:49.121 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:49.673 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:50.718 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:50.778 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:50.955 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:52.148 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 09:02:52.514 [redisson-netty-4-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:02:52.524 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:02:53.402 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:54.067 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:54.179 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:54.225 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:54.251 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:54.722 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:54.908 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:57.009 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:57.714 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:58.402 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:02.110 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:02.303 [main] INFO  o.scrm.scheduler.config.XxlJobConfig - >>>>>>>>>>> xxl-job config init.
2025-07-26 09:03:03.755 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:03:03.756 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:03:03.756 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:03:04.180 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:03:04.181 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:03:06.034 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:03:06.076 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-26 09:03:06.452 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:task30MinExecutor, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2c9c7f4a[class org.scrm.scheduler.task.ScheduledTask30Min#execute]
2025-07-26 09:03:06.452 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weAgentMsgDelaySendTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@d679e86[class org.scrm.scheduler.task.WeAgentMsgDelaySendTask#execute]
2025-07-26 09:03:06.452 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weChatMsgQiRuleNoticeTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@c634247[class org.scrm.scheduler.task.WeChatMsgQiRuleNoticeTask#execute]
2025-07-26 09:03:06.452 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:chatMsgQiRuleWeeklyNoticeTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@414c63f1[class org.scrm.scheduler.task.WeChatMsgQiRuleNoticeTask#chatMsgQiRuleWeeklyNotice]
2025-07-26 09:03:06.452 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weChatMsgQiRuleWeeklyStatisticsTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@e6522bd[class org.scrm.scheduler.task.WeChatMsgQiRuleWeeklyTask#weChatMsgQiRuleWeeklyStatisticsHandler]
2025-07-26 09:03:06.452 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weChatMsgQiRuleUserStatisticsTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3f471e59[class org.scrm.scheduler.task.WeChatMsgQiRuleWeeklyTask#weChatMsgQiRuleUserStatisticsHandler]
2025-07-26 09:03:06.452 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weGroupAndCustomerCountDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2920028c[class org.scrm.scheduler.task.WeCountDataPushTask#process]
2025-07-26 09:03:06.452 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weFormSurveyCatalogueTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@b51afb2[class org.scrm.scheduler.task.WeFormSurveyCatalogueTask#process]
2025-07-26 09:03:06.452 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weGroupChatStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2c0604af[class org.scrm.scheduler.task.WeGroupChatStatisticTask#process]
2025-07-26 09:03:06.452 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weGroupChatUserStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2b3472a0[class org.scrm.scheduler.task.WeGroupChatUserStatisticTask#process]
2025-07-26 09:03:06.452 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weGroupCodeCheck, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2a92128[class org.scrm.scheduler.task.WeGroupCodeTask#process]
2025-07-26 09:03:06.453 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weKfCustomerStatTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@48322f0[class org.scrm.scheduler.task.WeKfStatTask#customerProcess]
2025-07-26 09:03:06.453 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weKfUserStatTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@183c6db3[class org.scrm.scheduler.task.WeKfStatTask#userProcess]
2025-07-26 09:03:06.453 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weLeadsAutoRecoveryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1015bd66[class org.scrm.scheduler.task.WeLeadsAutoRecoveryTask#execute]
2025-07-26 09:03:06.453 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:sendWeMomentHandle, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5b7e6e2d[class org.scrm.scheduler.task.WeMomentTask#sendWeMomentHandle]
2025-07-26 09:03:06.453 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:wePermitUserListTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@46775310[class org.scrm.scheduler.task.WeMsgAuditTask#getPermitUserListHandle]
2025-07-26 09:03:06.453 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weChatMsgPullTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3dddef98[class org.scrm.scheduler.task.WeMsgAuditTask#eChatMsgPullHandle]
2025-07-26 09:03:06.453 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weCheckSingleAgreeTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1e9bc8[class org.scrm.scheduler.task.WeMsgAuditTask#checkSingleAgreeTaskHandle]
2025-07-26 09:03:06.453 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:aiHotWordAnalysis, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@28b992e0[class org.scrm.scheduler.task.WeMsgAuditTask#aiHotWordAnalysis]
2025-07-26 09:03:06.453 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weCheckRoomAgreeTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@542abb34[class org.scrm.scheduler.task.WeMsgAuditTask#checkRoomAgreeHandle]
2025-07-26 09:03:06.454 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weProductOrderStatisticsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6592f06[class org.scrm.scheduler.task.WeProductOrderStatisticsDataTask$$EnhancerBySpringCGLIB$$1bdc79f9#execute]
2025-07-26 09:03:06.454 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weProductOrderSyncTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@726d34de[class org.scrm.scheduler.task.WeProductOrderSyncTask#execute]
2025-07-26 09:03:06.455 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weQrCodeUpdateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@162796e1[class org.scrm.scheduler.task.WeQrCodeUpdateTask#process]
2025-07-26 09:03:06.455 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weCommonLinkStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@17556c0a[class org.scrm.scheduler.task.WeShortLinkCommonStatisticTask#commonLinkStatisticTask]
2025-07-26 09:03:06.455 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:shortLinkPromotionStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@58bb50f9[class org.scrm.scheduler.task.WeShortLinkPromotionStatisticTask#shortLinkPromotionStatisticHandle]
2025-07-26 09:03:06.455 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weShortLinkStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@43eb44f4[class org.scrm.scheduler.task.WeShortLinkStatisticTask#shortLinkStatisticHandle]
2025-07-26 09:03:06.455 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:createCycleGroupSopTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@69d142f6[class org.scrm.scheduler.task.WeSopTask#createCycleGroupSop]
2025-07-26 09:03:06.455 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:pushWeChatTypeTipTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@555856fa[class org.scrm.scheduler.task.WeSopTask#pushWeChatTypeTaskTip]
2025-07-26 09:03:06.455 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:earlyEndSopTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5766f830[class org.scrm.scheduler.task.WeSopTask#earlyEndSop]
2025-07-26 09:03:06.455 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weTaskFissionStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4eb3dacc[class org.scrm.scheduler.task.WeTaskFissionStatusTask#taskFissionExpiredStatusHandle]
2025-07-26 09:03:06.455 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:leadsLongTimeNotFollowUp, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2d16e71e[class org.scrm.scheduler.task.WeTasksTask#execute]
2025-07-26 09:03:06.455 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weUserBehaviorDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3a6e8bc0[class org.scrm.scheduler.task.WeUserBehaviorDataTask#process]
2025-07-26 09:03:06.733 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:06.743 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:06.749 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:06.756 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:06.761 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:06.767 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:06.973 [Thread-89] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 18081
2025-07-26 09:03:07.182 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6400"]
2025-07-26 09:03:07.195 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6400 (http) with context path ''
2025-07-26 09:03:07.198 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6400, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-task', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:03:07.198 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-task with instance: Instance{instanceId='null', ip='**************', port=6400, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:03:07.233 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-task **************:6400 register finished
2025-07-26 09:03:07.395 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [127.0.0.1:5672]
2025-07-26 09:03:07.917 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#12568b11:0/SimpleConnection@6603f03d [delegate=amqp://admin@127.0.0.1:5672/iyqueVhost, localPort= 51978]
2025-07-26 09:03:08.245 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-task@@DEFAULT -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:03:08.250 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task@@DEFAULT -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:03:08.304 [main] INFO  o.scrm.scheduler.ScrmTaskApplication - Started ScrmTaskApplication in 37.989 seconds (JVM running for 39.346)
2025-07-26 09:03:08.313 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:08.314 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:08.315 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-task.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:08.315 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-task.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:08.316 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-task-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:08.316 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-task-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:08.316 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-task+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:08.316 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-task, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:08.886 [RMI TCP Connection(12)-**************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 09:03:08.886 [RMI TCP Connection(12)-**************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-26 09:03:08.893 [RMI TCP Connection(12)-**************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 7 ms
2025-07-26 09:03:09.025 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:03:09.029 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:03:41.063 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:03:41.064 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:04:13.100 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:04:13.100 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:04:33.654 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [polling-resp] config changed. dataId=scrm-common.yml, group=DEFAULT_GROUP, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:04:33.654 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - get changedGroupKeys:[scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505]
2025-07-26 09:04:33.720 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [data-received] dataId=scrm-common.yml, group=DEFAULT_GROUP, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, md5=b0abac52cbc78c9c8caa15d5d5109373, content=scrm:
  baiduMapsAk: HMM2G1pfhLrrtjI2dgOvQtokNqazExUY
  h5Domain: https://wework.doha1000day.com/mob..., type=yaml
2025-07-26 09:04:33.720 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-context] dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373
2025-07-26 09:04:33.980 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:04:33.981 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:04:33.981 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:04:33.981 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:04:33.981 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:04:33.981 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:04:34.099 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[scrm-task] & group[DEFAULT_GROUP]
2025-07-26 09:04:34.161 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[scrm-task-dev.yml] & group[DEFAULT_GROUP]
2025-07-26 09:04:34.161 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-scrm-task-dev.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-task.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-task,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-common.yml,DEFAULT_GROUP'}]
2025-07-26 09:04:34.295 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.boot.SpringApplication - The following 1 profile is active: "dev"
2025-07-26 09:04:34.314 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.boot.SpringApplication - Started application in 0.589 seconds (JVM running for 125.356)
2025-07-26 09:04:34.714 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-error] dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@608b463c tx={}
org.springframework.boot.context.properties.bind.BindException: Failed to bind properties under 'spring.datasource.druid' to javax.sql.DataSource
	at org.springframework.boot.context.properties.bind.Binder.handleBindError(Binder.java:384)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:344)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:329)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:259)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:246)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBinder.bind(ConfigurationPropertiesBinder.java:95)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.bind(ConfigurationPropertiesBindingPostProcessor.java:89)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.postProcessBeforeInitialization(ConfigurationPropertiesBindingPostProcessor.java:78)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:431)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.rebind(ConfigurationPropertiesRebinder.java:105)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.rebind(ConfigurationPropertiesRebinder.java:83)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.onApplicationEvent(ConfigurationPropertiesRebinder.java:138)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.onApplicationEvent(ConfigurationPropertiesRebinder.java:51)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:421)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:378)
	at org.springframework.cloud.context.refresh.ContextRefresher.refreshEnvironment(ContextRefresher.java:103)
	at org.springframework.cloud.context.refresh.ContextRefresher.refresh(ContextRefresher.java:94)
	at org.springframework.cloud.endpoint.event.RefreshEventListener.handle(RefreshEventListener.java:72)
	at org.springframework.cloud.endpoint.event.RefreshEventListener.onApplicationEvent(RefreshEventListener.java:61)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:421)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:378)
	at com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1.innerReceive(NacosContextRefresher.java:133)
	at com.alibaba.nacos.api.config.listener.AbstractSharedListener.receiveConfigInfo(AbstractSharedListener.java:40)
	at com.alibaba.nacos.client.config.impl.CacheData$1.run(CacheData.java:210)
	at com.alibaba.nacos.client.config.impl.CacheData.safeNotifyListener(CacheData.java:241)
	at com.alibaba.nacos.client.config.impl.CacheData.checkListenerMd5(CacheData.java:180)
	at com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable.run(ClientWorker.java:569)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.IllegalStateException: Unable to set value for property url
	at org.springframework.boot.context.properties.bind.JavaBeanBinder$BeanProperty.setValue(JavaBeanBinder.java:367)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:101)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:83)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:59)
	at org.springframework.boot.context.properties.bind.Binder.lambda$bindDataObject$5(Binder.java:473)
	at org.springframework.boot.context.properties.bind.Binder$Context.withIncreasedDepth(Binder.java:587)
	at org.springframework.boot.context.properties.bind.Binder$Context.withDataObject(Binder.java:573)
	at org.springframework.boot.context.properties.bind.Binder$Context.access$300(Binder.java:534)
	at org.springframework.boot.context.properties.bind.Binder.bindDataObject(Binder.java:471)
	at org.springframework.boot.context.properties.bind.Binder.bindObject(Binder.java:411)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:340)
	... 41 common frames omitted
Caused by: java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder$BeanProperty.setValue(JavaBeanBinder.java:364)
	... 51 common frames omitted
Caused by: java.lang.UnsupportedOperationException: null
	at com.alibaba.druid.pool.DruidAbstractDataSource.setUrl(DruidAbstractDataSource.java:1203)
	... 56 common frames omitted
2025-07-26 09:04:34.715 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-listener] time cost=995ms in ClientWorker, dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@608b463c 
2025-07-26 09:04:40.332 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-26 09:04:40.332 [Thread-79] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-26 09:04:40.333 [Thread-79] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-26 09:04:40.334 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-26 09:04:40.340 [SpringApplicationShutdownHook] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-07-26 09:05:09.268 [main] INFO  o.scrm.scheduler.ScrmTaskApplication - The following 1 profile is active: "dev"
2025-07-26 09:05:09.300 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:05:09.301 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:05:09.301 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:05:09.301 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:05:09.301 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:05:09.301 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:05:09.301 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:05:11.767 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:05:11.769 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:05:11.803 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-07-26 09:05:12.431 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=827235e6-fd6d-3295-9b20-ec7c3f41506a
2025-07-26 09:05:12.882 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$56289780] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:12.915 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:12.925 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:12.929 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:12.934 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:12.952 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:12.955 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:12.956 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$532/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:12.968 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:12.978 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:13.027 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:13.048 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:13.073 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm.scheduler]'.
2025-07-26 09:05:13.496 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6400 (http)
2025-07-26 09:05:13.506 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6400"]
2025-07-26 09:05:13.507 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 09:05:13.507 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 09:05:13.618 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-26 09:05:13.619 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4318 ms
2025-07-26 09:05:14.412 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:05:15.825 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-26 09:05:25.037 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:25.541 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:26.740 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:26.801 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:26.999 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:28.158 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 09:05:28.587 [redisson-netty-4-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:05:28.597 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:05:29.531 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:30.254 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:30.330 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:30.370 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:30.396 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:30.779 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:30.900 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:32.563 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:32.953 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:33.644 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:37.231 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:37.448 [main] INFO  o.scrm.scheduler.config.XxlJobConfig - >>>>>>>>>>> xxl-job config init.
2025-07-26 09:05:39.427 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:05:39.428 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:05:39.428 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:05:39.918 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:05:39.921 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:05:44.449 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:05:44.484 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-26 09:05:44.898 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:task30MinExecutor, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@48430066[class org.scrm.scheduler.task.ScheduledTask30Min#execute]
2025-07-26 09:05:44.898 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weAgentMsgDelaySendTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6ddb5117[class org.scrm.scheduler.task.WeAgentMsgDelaySendTask#execute]
2025-07-26 09:05:44.898 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weChatMsgQiRuleNoticeTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@761eaf72[class org.scrm.scheduler.task.WeChatMsgQiRuleNoticeTask#execute]
2025-07-26 09:05:44.898 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:chatMsgQiRuleWeeklyNoticeTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@237b93f5[class org.scrm.scheduler.task.WeChatMsgQiRuleNoticeTask#chatMsgQiRuleWeeklyNotice]
2025-07-26 09:05:44.898 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weChatMsgQiRuleWeeklyStatisticsTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@73ebf7cc[class org.scrm.scheduler.task.WeChatMsgQiRuleWeeklyTask#weChatMsgQiRuleWeeklyStatisticsHandler]
2025-07-26 09:05:44.898 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weChatMsgQiRuleUserStatisticsTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@440f434f[class org.scrm.scheduler.task.WeChatMsgQiRuleWeeklyTask#weChatMsgQiRuleUserStatisticsHandler]
2025-07-26 09:05:44.898 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weGroupAndCustomerCountDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@47690183[class org.scrm.scheduler.task.WeCountDataPushTask#process]
2025-07-26 09:05:44.899 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weFormSurveyCatalogueTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@38f2c6a0[class org.scrm.scheduler.task.WeFormSurveyCatalogueTask#process]
2025-07-26 09:05:44.899 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weGroupChatStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@148b13e5[class org.scrm.scheduler.task.WeGroupChatStatisticTask#process]
2025-07-26 09:05:44.899 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weGroupChatUserStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2a202f00[class org.scrm.scheduler.task.WeGroupChatUserStatisticTask#process]
2025-07-26 09:05:44.899 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weGroupCodeCheck, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@25ca56de[class org.scrm.scheduler.task.WeGroupCodeTask#process]
2025-07-26 09:05:44.899 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weKfCustomerStatTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@b234301[class org.scrm.scheduler.task.WeKfStatTask#customerProcess]
2025-07-26 09:05:44.899 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weKfUserStatTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@47fb7dbb[class org.scrm.scheduler.task.WeKfStatTask#userProcess]
2025-07-26 09:05:44.899 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weLeadsAutoRecoveryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6c88026b[class org.scrm.scheduler.task.WeLeadsAutoRecoveryTask#execute]
2025-07-26 09:05:44.899 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:sendWeMomentHandle, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1b03d582[class org.scrm.scheduler.task.WeMomentTask#sendWeMomentHandle]
2025-07-26 09:05:44.899 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weCheckSingleAgreeTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@aa9b6a8[class org.scrm.scheduler.task.WeMsgAuditTask#checkSingleAgreeTaskHandle]
2025-07-26 09:05:44.899 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weCheckRoomAgreeTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@162304e3[class org.scrm.scheduler.task.WeMsgAuditTask#checkRoomAgreeHandle]
2025-07-26 09:05:44.899 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:wePermitUserListTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3a94d716[class org.scrm.scheduler.task.WeMsgAuditTask#getPermitUserListHandle]
2025-07-26 09:05:44.899 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:aiHotWordAnalysis, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3fbeaa03[class org.scrm.scheduler.task.WeMsgAuditTask#aiHotWordAnalysis]
2025-07-26 09:05:44.900 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weChatMsgPullTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@78e547aa[class org.scrm.scheduler.task.WeMsgAuditTask#eChatMsgPullHandle]
2025-07-26 09:05:44.901 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weProductOrderStatisticsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@766f5049[class org.scrm.scheduler.task.WeProductOrderStatisticsDataTask$$EnhancerBySpringCGLIB$$53793f0a#execute]
2025-07-26 09:05:44.901 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weProductOrderSyncTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6a5f05f5[class org.scrm.scheduler.task.WeProductOrderSyncTask#execute]
2025-07-26 09:05:44.901 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weQrCodeUpdateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@207b440f[class org.scrm.scheduler.task.WeQrCodeUpdateTask#process]
2025-07-26 09:05:44.902 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weCommonLinkStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@72f18fbd[class org.scrm.scheduler.task.WeShortLinkCommonStatisticTask#commonLinkStatisticTask]
2025-07-26 09:05:44.902 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:shortLinkPromotionStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7e9074b2[class org.scrm.scheduler.task.WeShortLinkPromotionStatisticTask#shortLinkPromotionStatisticHandle]
2025-07-26 09:05:44.902 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weShortLinkStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6cf2b638[class org.scrm.scheduler.task.WeShortLinkStatisticTask#shortLinkStatisticHandle]
2025-07-26 09:05:44.902 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:createCycleGroupSopTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1653cc95[class org.scrm.scheduler.task.WeSopTask#createCycleGroupSop]
2025-07-26 09:05:44.902 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:pushWeChatTypeTipTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@68212585[class org.scrm.scheduler.task.WeSopTask#pushWeChatTypeTaskTip]
2025-07-26 09:05:44.902 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:earlyEndSopTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1569e9d7[class org.scrm.scheduler.task.WeSopTask#earlyEndSop]
2025-07-26 09:05:44.902 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weTaskFissionStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6c53ce34[class org.scrm.scheduler.task.WeTaskFissionStatusTask#taskFissionExpiredStatusHandle]
2025-07-26 09:05:44.902 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:leadsLongTimeNotFollowUp, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@64ec804f[class org.scrm.scheduler.task.WeTasksTask#execute]
2025-07-26 09:05:44.902 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weUserBehaviorDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5008c5a[class org.scrm.scheduler.task.WeUserBehaviorDataTask#process]
2025-07-26 09:05:45.503 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:45.535 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:45.545 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:45.558 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:45.567 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:45.578 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:45.810 [Thread-97] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 18081
2025-07-26 09:05:46.116 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6400"]
2025-07-26 09:05:46.134 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6400 (http) with context path ''
2025-07-26 09:05:46.139 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6400, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-task', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:05:46.140 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-task with instance: Instance{instanceId='null', ip='**************', port=6400, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:05:46.179 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-task **************:6400 register finished
2025-07-26 09:05:46.404 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [127.0.0.1:5672]
2025-07-26 09:05:46.469 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#4cf66f73:0/SimpleConnection@1c4f3b3d [delegate=amqp://admin@127.0.0.1:5672/iyqueVhost, localPort= 52769]
2025-07-26 09:05:46.767 [main] INFO  o.scrm.scheduler.ScrmTaskApplication - Started ScrmTaskApplication in 39.946 seconds (JVM running for 41.481)
2025-07-26 09:05:46.780 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:05:46.782 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:05:46.783 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-task.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:05:46.783 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-task.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:05:46.784 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-task-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:05:46.784 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-task-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:05:46.784 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-task+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:05:46.785 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-task, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:05:47.199 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-task@@DEFAULT -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:05:47.206 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task@@DEFAULT -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:05:47.383 [RMI TCP Connection(7)-**************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 09:05:47.383 [RMI TCP Connection(7)-**************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-26 09:05:47.390 [RMI TCP Connection(7)-**************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-07-26 09:05:48.328 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:05:48.329 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:06:20.378 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:06:20.378 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:06:52.430 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:06:52.430 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:07:24.461 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:07:24.462 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:07:28.223 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.scheduler.listener.QwSopListener - sop任务构建：msg:{"createOrUpdate":true,"sopBaseId":1948913009719263233}
2025-07-26 09:07:29.880 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] ERROR o.s.s.s.impl.sop.SopTaskServiceImpl - 新客SOP{"id":1948913009719263233,"baseType":1,"businessType":7,"sopName":"拨打电话-A","sendType":2,"executeCustomerOrGroup":{"executeCustomerCondit":{"change":false,"executeCustomerQUECondits":[]},"crowdAttribute":{"change":false,"crowdIds":[]}},"executeCustomerSwipe":[],"endContent":{"executeTag":{"change":false,"tagIds":[]},"joinCustomerGroup":{"change":false,"joinGroupTip":"","groupCodeId":"","groupName":"","groupUrl":""},"toChangeIntoOtherSop":{"change":false,"toChangeTip":"","toChangeIntoSopId":""}},"sopState":1,"earlyEnd":0,"delFlag":0,"scopeType":1,"weCustomersQuery":{"genders":"","noContainTrackStates":"","customerTypes":"","externalUserid":"","externalUserids":"","firstUserId":"","name":"","tagIds":"etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw","userIds":"WangWenBo","deptIds":"","beginTime":"","endTime":"","excludeTagIds":"","dataScope":false,"stateList":[],"noTagCheck":false,"noRepeat":false,"isFilterLossCustomer":false,"ids":[],"phone":"","customerIds":"","isJoinBlacklist":1,"isContain":2,"tagNumber":0,"userNames":"王文博","tagNames":"测试A","lossBeginTime":"","lossEndTime":"","remarkCorpName":"","remarkCustomerName":""},"createBy":"","createById":0,"createTime":1753492045000,"updateBy":"","updateById":0,"updateTime":1753492045000,"params":{}}
2025-07-26 09:07:29.896 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:07:30.158 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-weSopExecuteTargets
2025-07-26 09:07:30.186 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:07:30, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:07:30.187 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:07:30, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:07:30.187 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:07:30, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:07:30.187 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:07:30, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:07:30.191 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-targetAttachments
2025-07-26 09:07:56.509 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:07:56.509 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:08:08.487 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.scheduler.listener.QwSopListener - sop任务构建：msg:{"createOrUpdate":false,"sopBaseId":1948913009719263233}
2025-07-26 09:08:08.511 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] ERROR o.s.s.s.impl.sop.SopTaskServiceImpl - 新客SOP{"id":1948913009719263233,"baseType":1,"businessType":7,"sopName":"拨打电话-A","sendType":2,"executeCustomerOrGroup":{"executeCustomerCondit":{"change":false,"executeCustomerQUECondits":[]},"crowdAttribute":{"change":false,"crowdIds":[]}},"executeCustomerSwipe":[],"endContent":{"executeTag":{"change":false,"tagIds":[]},"joinCustomerGroup":{"change":false,"joinGroupTip":"","groupCodeId":"","groupName":"","groupUrl":""},"toChangeIntoOtherSop":{"change":false,"toChangeTip":"","toChangeIntoSopId":""}},"sopState":2,"earlyEnd":0,"delFlag":0,"scopeType":1,"weCustomersQuery":{"genders":",1,2","noContainTrackStates":"","customerTypes":"","externalUserid":"","externalUserids":"","firstUserId":"","name":"","tagIds":"etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw","userIds":"WangWenBo","deptIds":"","beginTime":"","endTime":"","excludeTagIds":"","dataScope":false,"stateList":[],"noTagCheck":false,"noRepeat":false,"isFilterLossCustomer":false,"ids":[],"phone":"","customerIds":"","isJoinBlacklist":1,"isContain":2,"tagNumber":0,"userNames":"王文博","tagNames":"测试A","lossBeginTime":"","lossEndTime":"","remarkCorpName":"","remarkCustomerName":""},"createBy":"","createById":0,"createTime":1753492045000,"updateBy":"","updateById":0,"updateTime":1753492088000,"params":{}}
2025-07-26 09:08:08.511 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:08:08.567 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP编辑模式：开始处理不满足条件的客户，SOP ID: 1948913009719263233, 当前满足条件的客户数量: 2
2025-07-26 09:08:08.596 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.i.WeSopExecuteTargetServiceImpl - 开始处理SOP编辑时的异常结束逻辑，SOP ID: 1948913009719263233, 当前满足条件的客户/群数量: 2
2025-07-26 09:08:08.656 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.i.WeSopExecuteTargetServiceImpl - SOP 1948913009719263233 下没有需要异常结束的目标
2025-07-26 09:08:08.715 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP编辑模式：完成处理不满足条件的客户，SOP ID: 1948913009719263233
2025-07-26 09:08:08.819 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-weSopExecuteTargets
2025-07-26 09:08:08.840 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:08:08, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:08:08.841 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:08:08, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:08:08.841 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:08:08, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:08:08.842 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:08:08, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:08:08.842 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:08:08, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:08:08.843 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:08:08, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:08:08.843 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:08:08, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:08:08.843 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:08:08, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:08:08.844 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:08:08, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:08:08.844 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:08:08, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:08:08.844 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:08:08, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:08:08.845 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:08:08, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:08:08.845 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-targetAttachments
2025-07-26 09:08:28.565 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:08:28.565 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:09:00.615 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:09:00.615 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:09:32.660 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:09:32.660 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:09:39.528 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.scheduler.listener.QwSopListener - sop任务构建：msg:{"createOrUpdate":true,"sopBaseId":1948913572485808129}
2025-07-26 09:09:44.493 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] ERROR o.s.s.s.impl.sop.SopTaskServiceImpl - 新客SOP{"id":1948913572485808129,"baseType":1,"businessType":7,"sopName":"拨打电话SOP","sendType":2,"executeCustomerOrGroup":{"executeCustomerCondit":{"change":false,"executeCustomerQUECondits":[]},"crowdAttribute":{"change":false,"crowdIds":[]}},"executeCustomerSwipe":[],"endContent":{"executeTag":{"change":false,"tagIds":[]},"joinCustomerGroup":{"change":false,"joinGroupTip":"","groupCodeId":"","groupName":"","groupUrl":""},"toChangeIntoOtherSop":{"change":false,"toChangeTip":"","toChangeIntoSopId":""}},"sopState":1,"earlyEnd":0,"delFlag":0,"scopeType":1,"weCustomersQuery":{"genders":"","noContainTrackStates":"","customerTypes":"","externalUserid":"","externalUserids":"","firstUserId":"","name":"","tagIds":"etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw","userIds":"WangWenBo","deptIds":"","beginTime":"","endTime":"","excludeTagIds":"","dataScope":false,"stateList":[],"noTagCheck":false,"noRepeat":false,"isFilterLossCustomer":false,"ids":[],"phone":"","customerIds":"","isJoinBlacklist":1,"isContain":2,"tagNumber":0,"userNames":"王文博","tagNames":"测试A","lossBeginTime":"","lossEndTime":"","remarkCorpName":"","remarkCustomerName":""},"createBy":"","createById":0,"createTime":1753492179000,"updateBy":"","updateById":0,"updateTime":1753492179000,"params":{}}
2025-07-26 09:09:44.494 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:09:44.798 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-weSopExecuteTargets
2025-07-26 09:09:44.836 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:09:44, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:09:44.838 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:09:44, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:09:44.841 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:09:44, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:09:44.843 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:09:44, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:09:44.845 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-targetAttachments
2025-07-26 09:10:04.705 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:10:04.706 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:10:36.757 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:10:36.757 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:10:51.081 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.scheduler.listener.QwSopListener - sop任务构建：msg:{"createOrUpdate":false,"sopBaseId":1948913572485808129}
2025-07-26 09:10:55.380 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] ERROR o.s.s.s.impl.sop.SopTaskServiceImpl - 新客SOP{"id":1948913572485808129,"baseType":1,"businessType":7,"sopName":"拨打电话SOP","sendType":2,"executeCustomerOrGroup":{"executeCustomerCondit":{"change":false,"executeCustomerQUECondits":[]},"crowdAttribute":{"change":false,"crowdIds":[]}},"executeCustomerSwipe":[],"endContent":{"executeTag":{"change":false,"tagIds":[]},"joinCustomerGroup":{"change":false,"joinGroupTip":"","groupCodeId":"","groupName":"","groupUrl":""},"toChangeIntoOtherSop":{"change":false,"toChangeTip":"","toChangeIntoSopId":""}},"sopState":2,"earlyEnd":0,"delFlag":0,"scopeType":1,"weCustomersQuery":{"genders":"","noContainTrackStates":"","customerTypes":"","externalUserid":"","externalUserids":"","firstUserId":"","name":"","tagIds":"etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw","userIds":"WangWenBo","deptIds":"","beginTime":"","endTime":"","excludeTagIds":"","dataScope":false,"stateList":[],"noTagCheck":false,"noRepeat":false,"isFilterLossCustomer":false,"ids":[],"phone":"","customerIds":"","isJoinBlacklist":1,"isContain":2,"tagNumber":0,"userNames":"王文博","tagNames":"测试A","lossBeginTime":"","lossEndTime":"","remarkCorpName":"","remarkCustomerName":""},"createBy":"","createById":0,"createTime":1753492179000,"updateBy":"","updateById":0,"updateTime":1753492251000,"params":{}}
2025-07-26 09:10:55.381 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:11:10.302 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP编辑模式：开始处理不满足条件的客户，SOP ID: 1948913572485808129, 当前满足条件的客户数量: 2
2025-07-26 09:11:17.683 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:11:17.683 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:11:25.311 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.i.WeSopExecuteTargetServiceImpl - 开始处理SOP编辑时的异常结束逻辑，SOP ID: 1948913572485808129, 当前满足条件的客户/群数量: 2
2025-07-26 09:11:37.656 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.i.WeSopExecuteTargetServiceImpl - SOP 1948913572485808129 下没有需要异常结束的目标
2025-07-26 09:11:45.593 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP编辑模式：完成处理不满足条件的客户，SOP ID: 1948913572485808129
2025-07-26 09:11:45.692 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-weSopExecuteTargets
2025-07-26 09:11:45.718 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:11:45, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:11:45.719 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:11:45, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:11:45.719 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:11:45, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:11:45.720 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:11:45, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:11:45.721 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:11:45, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:11:45.721 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:11:45, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:11:45.721 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:11:45, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:11:45.722 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:11:45, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:11:45.722 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:11:45, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:11:45.723 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:11:45, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:11:45.723 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:11:45, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:11:45.723 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:11:45, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:11:45.724 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#31-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-targetAttachments
2025-07-26 09:11:49.730 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:11:49.730 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:12:21.772 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:12:21.772 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:12:53.821 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:12:53.821 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:13:25.875 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:13:25.875 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:13:57.936 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:13:57.936 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:14:29.984 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:14:29.986 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:15:02.033 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:15:02.034 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:15:34.079 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:15:34.079 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:16:06.130 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:16:06.131 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:16:38.188 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:16:38.188 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:17:10.239 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:17:10.240 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:17:37.895 [Thread-79] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-26 09:17:37.895 [Thread-79] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-26 09:17:37.896 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-26 09:17:37.897 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-26 09:17:37.906 [SpringApplicationShutdownHook] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-07-26 09:39:59.206 [main] INFO  o.scrm.scheduler.ScrmTaskApplication - The following 1 profile is active: "dev"
2025-07-26 09:39:59.235 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:39:59.235 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:39:59.235 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:39:59.235 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:39:59.235 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:39:59.236 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:39:59.236 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:40:03.793 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:40:03.798 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:40:03.838 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-07-26 09:40:05.220 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=827235e6-fd6d-3295-9b20-ec7c3f41506a
2025-07-26 09:40:05.889 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$b6c5c408] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:05.932 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:05.945 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:05.949 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:05.960 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:05.973 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:05.977 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:05.980 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$532/594169136] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:05.997 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:06.018 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:06.088 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:06.110 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:06.152 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm.scheduler]'.
2025-07-26 09:40:06.847 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6400 (http)
2025-07-26 09:40:06.894 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6400"]
2025-07-26 09:40:06.895 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 09:40:06.895 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 09:40:07.365 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-26 09:40:07.365 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 8129 ms
2025-07-26 09:40:08.479 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:40:10.047 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-26 09:40:24.015 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:24.656 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:26.034 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:26.089 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:26.225 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:27.220 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 09:40:27.608 [redisson-netty-4-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:40:27.616 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:40:28.538 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:29.198 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:29.279 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:29.332 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:29.364 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:29.792 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:29.908 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:31.732 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:32.068 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:32.827 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:35.401 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:35.533 [main] INFO  o.scrm.scheduler.config.XxlJobConfig - >>>>>>>>>>> xxl-job config init.
2025-07-26 09:40:36.540 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:40:36.541 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:40:36.542 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:40:36.875 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:40:36.877 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:40:38.648 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:40:38.676 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-26 09:40:38.902 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:task30MinExecutor, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6400e810[class org.scrm.scheduler.task.ScheduledTask30Min#execute]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weAgentMsgDelaySendTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3d74313a[class org.scrm.scheduler.task.WeAgentMsgDelaySendTask#execute]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weChatMsgQiRuleNoticeTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@8108420[class org.scrm.scheduler.task.WeChatMsgQiRuleNoticeTask#execute]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:chatMsgQiRuleWeeklyNoticeTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2d2325d0[class org.scrm.scheduler.task.WeChatMsgQiRuleNoticeTask#chatMsgQiRuleWeeklyNotice]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weChatMsgQiRuleWeeklyStatisticsTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4e80180c[class org.scrm.scheduler.task.WeChatMsgQiRuleWeeklyTask#weChatMsgQiRuleWeeklyStatisticsHandler]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weChatMsgQiRuleUserStatisticsTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@52e10d07[class org.scrm.scheduler.task.WeChatMsgQiRuleWeeklyTask#weChatMsgQiRuleUserStatisticsHandler]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weGroupAndCustomerCountDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5edca2ff[class org.scrm.scheduler.task.WeCountDataPushTask#process]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weFormSurveyCatalogueTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6a651151[class org.scrm.scheduler.task.WeFormSurveyCatalogueTask#process]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weGroupChatStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@246b2469[class org.scrm.scheduler.task.WeGroupChatStatisticTask#process]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weGroupChatUserStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7e389cd6[class org.scrm.scheduler.task.WeGroupChatUserStatisticTask#process]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weGroupCodeCheck, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@45c812b4[class org.scrm.scheduler.task.WeGroupCodeTask#process]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weKfCustomerStatTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7657cc5[class org.scrm.scheduler.task.WeKfStatTask#customerProcess]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weKfUserStatTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7a50482c[class org.scrm.scheduler.task.WeKfStatTask#userProcess]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weLeadsAutoRecoveryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@40b4a59c[class org.scrm.scheduler.task.WeLeadsAutoRecoveryTask#execute]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:sendWeMomentHandle, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@456c528c[class org.scrm.scheduler.task.WeMomentTask#sendWeMomentHandle]
2025-07-26 09:40:38.903 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weCheckRoomAgreeTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@69438db0[class org.scrm.scheduler.task.WeMsgAuditTask#checkRoomAgreeHandle]
2025-07-26 09:40:38.904 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weCheckSingleAgreeTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@112aa99c[class org.scrm.scheduler.task.WeMsgAuditTask#checkSingleAgreeTaskHandle]
2025-07-26 09:40:38.904 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:aiHotWordAnalysis, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@74d76660[class org.scrm.scheduler.task.WeMsgAuditTask#aiHotWordAnalysis]
2025-07-26 09:40:38.904 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:wePermitUserListTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5308b244[class org.scrm.scheduler.task.WeMsgAuditTask#getPermitUserListHandle]
2025-07-26 09:40:38.904 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weChatMsgPullTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@26d38b98[class org.scrm.scheduler.task.WeMsgAuditTask#eChatMsgPullHandle]
2025-07-26 09:40:38.904 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weProductOrderStatisticsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6271a097[class org.scrm.scheduler.task.WeProductOrderStatisticsDataTask$$EnhancerBySpringCGLIB$$254092a2#execute]
2025-07-26 09:40:38.905 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weProductOrderSyncTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2dda3edf[class org.scrm.scheduler.task.WeProductOrderSyncTask#execute]
2025-07-26 09:40:38.905 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weQrCodeUpdateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3bd623b2[class org.scrm.scheduler.task.WeQrCodeUpdateTask#process]
2025-07-26 09:40:38.905 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weCommonLinkStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1e3e01b[class org.scrm.scheduler.task.WeShortLinkCommonStatisticTask#commonLinkStatisticTask]
2025-07-26 09:40:38.905 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:shortLinkPromotionStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6ef99da8[class org.scrm.scheduler.task.WeShortLinkPromotionStatisticTask#shortLinkPromotionStatisticHandle]
2025-07-26 09:40:38.905 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weShortLinkStatisticTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@12a96a6e[class org.scrm.scheduler.task.WeShortLinkStatisticTask#shortLinkStatisticHandle]
2025-07-26 09:40:38.905 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:pushWeChatTypeTipTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@18e8003a[class org.scrm.scheduler.task.WeSopTask#pushWeChatTypeTaskTip]
2025-07-26 09:40:38.905 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:createCycleGroupSopTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@695d804[class org.scrm.scheduler.task.WeSopTask#createCycleGroupSop]
2025-07-26 09:40:38.905 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:earlyEndSopTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@24a301a3[class org.scrm.scheduler.task.WeSopTask#earlyEndSop]
2025-07-26 09:40:38.905 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weTaskFissionStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7d80f543[class org.scrm.scheduler.task.WeTaskFissionStatusTask#taskFissionExpiredStatusHandle]
2025-07-26 09:40:38.905 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:leadsLongTimeNotFollowUp, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2531c319[class org.scrm.scheduler.task.WeTasksTask#execute]
2025-07-26 09:40:38.905 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:weUserBehaviorDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@d4c6a43[class org.scrm.scheduler.task.WeUserBehaviorDataTask#process]
2025-07-26 09:40:39.142 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:39.153 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:39.158 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:39.164 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:39.169 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:39.173 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:39.306 [Thread-98] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 18081
2025-07-26 09:40:39.577 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6400"]
2025-07-26 09:40:39.588 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6400 (http) with context path ''
2025-07-26 09:40:39.591 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6400, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-task', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:40:39.592 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-task with instance: Instance{instanceId='null', ip='**************', port=6400, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:40:39.637 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-task **************:6400 register finished
2025-07-26 09:40:39.803 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [127.0.0.1:5672]
2025-07-26 09:40:39.970 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#5ac168e3:0/SimpleConnection@1c77ab1 [delegate=amqp://admin@127.0.0.1:5672/iyqueVhost, localPort= 55074]
2025-07-26 09:40:40.266 [main] INFO  o.scrm.scheduler.ScrmTaskApplication - Started ScrmTaskApplication in 43.419 seconds (JVM running for 44.912)
2025-07-26 09:40:40.278 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:40:40.279 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:40:40.281 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-task.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:40:40.281 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-task.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:40:40.282 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-task-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:40:40.282 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-task-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:40:40.282 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-task+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:40:40.282 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-task, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:40:40.528 [RMI TCP Connection(14)-**************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 09:40:40.528 [RMI TCP Connection(14)-**************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-26 09:40:40.533 [RMI TCP Connection(14)-**************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-26 09:40:40.618 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-task@@DEFAULT -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:40:40.623 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task@@DEFAULT -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:40:41.349 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:40:41.351 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:41:13.404 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:41:13.404 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:41:45.450 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:41:45.450 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:42:05.429 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.scheduler.listener.QwSopListener - sop任务构建：msg:{"createOrUpdate":true,"sopBaseId":1948921733280243714}
2025-07-26 09:42:17.907 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] ERROR o.s.s.s.impl.sop.SopTaskServiceImpl - 新客SOP{"id":1948921733280243714,"baseType":1,"businessType":7,"sopName":"拨打电话-A","sendType":2,"executeCustomerOrGroup":{"executeCustomerCondit":{"change":false,"executeCustomerQUECondits":[]},"crowdAttribute":{"change":false,"crowdIds":[]}},"executeCustomerSwipe":[],"endContent":{"executeTag":{"change":false,"tagIds":[]},"joinCustomerGroup":{"change":false,"joinGroupTip":"","groupCodeId":"","groupName":"","groupUrl":""},"toChangeIntoOtherSop":{"change":false,"toChangeTip":"","toChangeIntoSopId":""}},"sopState":1,"earlyEnd":0,"delFlag":0,"scopeType":1,"weCustomersQuery":{"genders":"","noContainTrackStates":"","customerTypes":"","externalUserid":"","externalUserids":"","firstUserId":"","name":"","tagIds":"etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw,etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ","userIds":"WangWenBo","deptIds":"","beginTime":"","endTime":"","excludeTagIds":"","dataScope":false,"stateList":[],"noTagCheck":false,"noRepeat":false,"isFilterLossCustomer":false,"ids":[],"phone":"","customerIds":"","isJoinBlacklist":1,"isContain":2,"tagNumber":0,"userNames":"王文博","tagNames":"测试A,测试B","lossBeginTime":"","lossEndTime":"","remarkCorpName":"","remarkCustomerName":""},"createBy":"","createById":0,"createTime":1753494125000,"updateBy":"","updateById":0,"updateTime":1753494125000,"params":{}}
2025-07-26 09:42:18.001 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:42:30.030 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:42:34.149 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:42:35.534 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-weSopExecuteTargets
2025-07-26 09:42:35.592 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:42:35, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:42:35.593 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:42:35, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:42:35.595 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:42:35, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:42:35.608 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:42:35, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:42:35.628 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-targetAttachments
2025-07-26 09:43:06.189 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:43:06.189 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:43:26.001 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.scheduler.listener.QwSopListener - sop任务构建：msg:{"createOrUpdate":false,"sopBaseId":1948921733280243714}
2025-07-26 09:43:33.927 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] ERROR o.s.s.s.impl.sop.SopTaskServiceImpl - 新客SOP{"id":1948921733280243714,"baseType":1,"businessType":7,"sopName":"拨打电话-A","sendType":2,"executeCustomerOrGroup":{"executeCustomerCondit":{"change":false,"executeCustomerQUECondits":[]},"crowdAttribute":{"change":false,"crowdIds":[]}},"executeCustomerSwipe":[],"endContent":{"executeTag":{"change":false,"tagIds":[]},"joinCustomerGroup":{"change":false,"joinGroupTip":"","groupCodeId":"","groupName":"","groupUrl":""},"toChangeIntoOtherSop":{"change":false,"toChangeTip":"","toChangeIntoSopId":""}},"sopState":2,"earlyEnd":0,"delFlag":0,"scopeType":1,"weCustomersQuery":{"genders":"","noContainTrackStates":"","customerTypes":"","externalUserid":"","externalUserids":"","firstUserId":"","name":"","tagIds":"etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw,etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ","userIds":"WangWenBo","deptIds":"","beginTime":"","endTime":"","excludeTagIds":"","dataScope":false,"stateList":[],"noTagCheck":false,"noRepeat":false,"isFilterLossCustomer":false,"ids":[],"phone":"","customerIds":"","isJoinBlacklist":1,"isContain":2,"tagNumber":0,"userNames":"王文博","tagNames":"测试A,测试B","lossBeginTime":"","lossEndTime":"","remarkCorpName":"","remarkCustomerName":""},"createBy":"","createById":0,"createTime":1753494125000,"updateBy":"","updateById":0,"updateTime":1753494206000,"params":{}}
2025-07-26 09:43:33.927 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:43:39.605 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:43:39.606 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:43:41.690 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP编辑模式：开始处理不满足条件的客户，SOP ID: 1948921733280243714, 当前满足条件的客户数量: 2
2025-07-26 09:43:50.075 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.i.WeSopExecuteTargetServiceImpl - 开始处理SOP编辑时的异常结束逻辑，SOP ID: 1948921733280243714, 当前满足条件的客户/群数量: 2
2025-07-26 09:43:50.075 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.i.WeSopExecuteTargetServiceImpl - SOP编辑去重处理：开始检查重复执行目标，SOP ID: 1948921733280243714, 客户数量: 2
2025-07-26 09:43:50.141 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.i.WeSopExecuteTargetServiceImpl - SOP编辑去重处理：完成，SOP ID: 1948921733280243714, 删除重复记录数量: 0
2025-07-26 09:43:50.178 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.i.WeSopExecuteTargetServiceImpl - SOP 1948921733280243714 下没有需要异常结束的目标
2025-07-26 09:43:50.256 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP编辑模式：完成处理不满足条件的客户，SOP ID: 1948921733280243714
2025-07-26 09:43:50.384 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-weSopExecuteTargets
2025-07-26 09:43:50.413 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:43:50, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:43:50.414 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:43:50, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:43:50.414 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:43:50, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:43:50.415 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:43:50, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:43:50.415 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:43:50, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:43:50.415 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:43:50, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:43:50.416 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:43:50, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:43:50.416 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:43:50, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:43:50.417 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:43:50, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:43:50.417 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:43:50, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:43:50.418 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:43:50, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:43:50.418 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:43:50, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:43:50.419 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-targetAttachments
2025-07-26 09:44:11.654 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:44:11.654 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:44:43.712 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:44:43.712 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:44:45.111 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.scheduler.listener.QwSopListener - sop任务构建：msg:{"createOrUpdate":true,"sopBaseId":1948922403609714690}
2025-07-26 09:44:46.519 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] ERROR o.s.s.s.impl.sop.SopTaskServiceImpl - 新客SOP{"id":1948922403609714690,"baseType":1,"businessType":7,"sopName":"拨打电话-A","sendType":2,"executeCustomerOrGroup":{"executeCustomerCondit":{"change":false,"executeCustomerQUECondits":[]},"crowdAttribute":{"change":false,"crowdIds":[]}},"executeCustomerSwipe":[],"endContent":{"executeTag":{"change":false,"tagIds":[]},"joinCustomerGroup":{"change":false,"joinGroupTip":"","groupCodeId":"","groupName":"","groupUrl":""},"toChangeIntoOtherSop":{"change":false,"toChangeTip":"","toChangeIntoSopId":""}},"sopState":1,"earlyEnd":0,"delFlag":0,"scopeType":1,"weCustomersQuery":{"genders":"","noContainTrackStates":"","customerTypes":"","externalUserid":"","externalUserids":"","firstUserId":"","name":"","tagIds":"etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw,etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ","userIds":"","deptIds":"","beginTime":"","endTime":"","excludeTagIds":"","dataScope":false,"stateList":[],"noTagCheck":false,"noRepeat":false,"isFilterLossCustomer":false,"ids":[],"phone":"","customerIds":"","isJoinBlacklist":1,"isContain":2,"tagNumber":0,"userNames":"","tagNames":"测试A,测试B","lossBeginTime":"","lossEndTime":"","remarkCorpName":"","remarkCustomerName":""},"createBy":"","createById":0,"createTime":1753494285000,"updateBy":"","updateById":0,"updateTime":1753494285000,"params":{}}
2025-07-26 09:44:46.520 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:44:49.523 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-weSopExecuteTargets
2025-07-26 09:44:49.586 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.586 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.603 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.604 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.604 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.604 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.606 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.606 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.608 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.608 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.608 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.609 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.609 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.609 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.609 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.610 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.610 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.610 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:44:49, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:44:49.611 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-targetAttachments
2025-07-26 09:45:15.778 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:45:15.778 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:45:32.987 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.scheduler.listener.QwSopListener - sop任务构建：msg:{"createOrUpdate":true,"sopBaseId":1948922604726591490}
2025-07-26 09:45:34.493 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] ERROR o.s.s.s.impl.sop.SopTaskServiceImpl - 新客SOP{"id":1948922604726591490,"baseType":1,"businessType":7,"sopName":"拨打电话-A","sendType":2,"executeCustomerOrGroup":{"executeCustomerCondit":{"change":false,"executeCustomerQUECondits":[]},"crowdAttribute":{"change":false,"crowdIds":[]}},"executeCustomerSwipe":[],"endContent":{"executeTag":{"change":false,"tagIds":[]},"joinCustomerGroup":{"change":false,"joinGroupTip":"","groupCodeId":"","groupName":"","groupUrl":""},"toChangeIntoOtherSop":{"change":false,"toChangeTip":"","toChangeIntoSopId":""}},"sopState":1,"earlyEnd":0,"delFlag":0,"scopeType":1,"weCustomersQuery":{"genders":"","noContainTrackStates":"","customerTypes":"","externalUserid":"","externalUserids":"","firstUserId":"","name":"","tagIds":"etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw,etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ","userIds":"WangWenBo","deptIds":"","beginTime":"","endTime":"","excludeTagIds":"","dataScope":false,"stateList":[],"noTagCheck":false,"noRepeat":false,"isFilterLossCustomer":false,"ids":[],"phone":"","customerIds":"","isJoinBlacklist":1,"isContain":2,"tagNumber":0,"userNames":"王文博","tagNames":"测试A,测试B","lossBeginTime":"","lossEndTime":"","remarkCorpName":"","remarkCustomerName":""},"createBy":"","createById":0,"createTime":1753494333000,"updateBy":"","updateById":0,"updateTime":1753494333000,"params":{}}
2025-07-26 09:45:34.494 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:45:37.068 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-weSopExecuteTargets
2025-07-26 09:45:37.094 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:45:37, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:45:37.095 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:45:37, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:45:37.095 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:45:37, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:45:37.096 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:45:37, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:45:37.096 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-targetAttachments
2025-07-26 09:45:47.831 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:45:47.831 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:46:07.282 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.scheduler.listener.QwSopListener - sop任务构建：msg:{"createOrUpdate":false,"sopBaseId":1948922604726591490}
2025-07-26 09:46:12.628 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] ERROR o.s.s.s.impl.sop.SopTaskServiceImpl - 新客SOP{"id":1948922604726591490,"baseType":1,"businessType":7,"sopName":"拨打电话-A","sendType":2,"executeCustomerOrGroup":{"executeCustomerCondit":{"change":false,"executeCustomerQUECondits":[]},"crowdAttribute":{"change":false,"crowdIds":[]}},"executeCustomerSwipe":[],"endContent":{"executeTag":{"change":false,"tagIds":[]},"joinCustomerGroup":{"change":false,"joinGroupTip":"","groupCodeId":"","groupName":"","groupUrl":""},"toChangeIntoOtherSop":{"change":false,"toChangeTip":"","toChangeIntoSopId":""}},"sopState":2,"earlyEnd":0,"delFlag":0,"scopeType":1,"weCustomersQuery":{"genders":"","noContainTrackStates":"","customerTypes":"","externalUserid":"","externalUserids":"","firstUserId":"","name":"","tagIds":"etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw,etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ","userIds":"WangWenBo","deptIds":"","beginTime":"","endTime":"","excludeTagIds":"","dataScope":false,"stateList":[],"noTagCheck":false,"noRepeat":false,"isFilterLossCustomer":false,"ids":[],"phone":"","customerIds":"","isJoinBlacklist":1,"isContain":2,"tagNumber":0,"userNames":"王文博","tagNames":"测试A,测试B","lossBeginTime":"","lossEndTime":"","remarkCorpName":"","remarkCustomerName":""},"createBy":"","createById":0,"createTime":1753494333000,"updateBy":"","updateById":0,"updateTime":1753494367000,"params":{}}
2025-07-26 09:46:12.628 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:47:24.829 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP编辑模式：开始处理不满足条件的客户，SOP ID: 1948922604726591490, 当前满足条件的客户数量: 2
2025-07-26 09:47:24.829 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-task with instance: Instance{instanceId='null', ip='**************', port=6400, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='DEFAULT_GROUP@@scrm-task', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:47:24.865 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@scrm-task@@DEFAULT -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:47:24.866 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@scrm-task@@DEFAULT -> []
2025-07-26 09:47:25.313 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.i.WeSopExecuteTargetServiceImpl - 开始处理SOP编辑时的异常结束逻辑，SOP ID: 1948922604726591490, 当前满足条件的客户/群数量: 2
2025-07-26 09:47:25.313 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.i.WeSopExecuteTargetServiceImpl - SOP编辑去重处理：开始检查重复执行目标，SOP ID: 1948922604726591490, 客户数量: 2
2025-07-26 09:47:25.344 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.i.WeSopExecuteTargetServiceImpl - SOP编辑去重处理：完成，SOP ID: 1948922604726591490, 删除重复记录数量: 0
2025-07-26 09:47:25.397 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.i.WeSopExecuteTargetServiceImpl - SOP 1948922604726591490 下没有需要异常结束的目标
2025-07-26 09:47:25.458 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP编辑模式：完成处理不满足条件的客户，SOP ID: 1948922604726591490
2025-07-26 09:47:25.626 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-weSopExecuteTargets
2025-07-26 09:47:25.658 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:47:25, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:47:25.659 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:47:25, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:47:25.659 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:47:25, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:47:25.659 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:47:25, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:47:25.660 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:47:25, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:47:25.660 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:47:25, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:47:25.661 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:47:25, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:47:25.661 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:47:25, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:47:25.661 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:47:25, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:47:25.661 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:47:25, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-27
2025-07-26 09:47:25.662 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:47:25, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:47:25.662 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-26 09:47:25, 原推送时间窗口: 00:15:00-00:30:00, 延后推送日期: 2025-07-27
2025-07-26 09:47:25.662 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#28-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-targetAttachments
2025-07-26 09:47:26.881 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:47:26.881 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:47:26.916 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-task@@DEFAULT -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:47:26.918 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task@@DEFAULT -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:47:58.924 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:47:58.925 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:48:30.973 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:48:30.973 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:49:03.029 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:49:03.029 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:49:35.062 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:49:35.062 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:50:07.110 [xxl-job, executor ExecutorRegistryThread] ERROR c.x.job.core.util.XxlJobRemotingUtil - Connection refused: connect
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1226)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at com.xxl.job.core.util.XxlJobRemotingUtil.postBody(XxlJobRemotingUtil.java:99)
	at com.xxl.job.core.biz.client.AdminBizClient.registry(AdminBizClient.java:42)
	at com.xxl.job.core.thread.ExecutorRegistryThread$1.run(ExecutorRegistryThread.java:48)
	at java.lang.Thread.run(Thread.java:748)
2025-07-26 09:50:07.111 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='scrm-scheduler', registryValue='http://127.0.0.1:18081/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connection refused: connect), for url : http://127.0.0.1:18080/xxl-job-admin/api/registry, content=null]
2025-07-26 09:50:11.947 [Thread-88] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-26 09:50:11.947 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-26 09:50:11.947 [Thread-88] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-26 09:50:11.947 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-26 09:50:11.954 [SpringApplicationShutdownHook] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
