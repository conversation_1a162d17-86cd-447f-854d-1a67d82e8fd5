package org.scrm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.scrm.base.constant.Constants;
import org.scrm.base.enums.SopExecuteStatus;
import org.scrm.base.enums.SopType;
import org.scrm.base.utils.StringUtils;
import org.scrm.config.rabbitmq.RabbitMQSettingConfig;
import org.scrm.domain.WeGroupMember;
import org.scrm.domain.WeSopChange;
import org.scrm.domain.WeTag;
import org.scrm.domain.customer.WeMakeCustomerTag;
import org.scrm.domain.customer.query.WeCustomersQuery;
import org.scrm.domain.groupchat.query.WeGroupChatQuery;
import org.scrm.domain.groupchat.query.WeMakeGroupTagQuery;
import org.scrm.domain.groupchat.vo.LinkGroupChatListVo;
import org.scrm.domain.groupcode.entity.WeGroupCode;
import org.scrm.domain.sop.WeSopBase;
import org.scrm.domain.sop.WeSopExecuteTarget;
import org.scrm.domain.sop.WeSopExecuteTargetAttachments;
import org.scrm.domain.sop.vo.WeSopExecuteEndVo;
import org.scrm.mapper.WeCustomerMapper;
import org.scrm.mapper.WeSopExecuteTargetMapper;
import org.scrm.service.*;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
* <AUTHOR>
* @description 针对表【we_sop_execute_target(目标执行对象表)】的数据库操作Service实现
* @createDate 2022-09-13 16:26:00
*/
@Slf4j
@Service
public class WeSopExecuteTargetServiceImpl extends ServiceImpl<WeSopExecuteTargetMapper, WeSopExecuteTarget>
implements IWeSopExecuteTargetService {


    @Autowired
    @Lazy
    private IWeSopBaseService iWeSopBaseService;

    @Autowired
    private IWeGroupService iWeGroupService;

    @Autowired
    private IWeCustomerService iWeCustomerService;

    @Autowired
    private IWeGroupCodeService iWeGroupCodeService;


    @Autowired
    private IWeGroupMemberService iWeGroupMemberService;


    @Autowired
    private IWeSopExecuteTargetAttachmentsService iWeSopExecuteTargetAttachmentsService;


    @Autowired
    private IWeGroupTagRelService weGroupTagRelService;

    @Autowired
    private IWeSopChangeService iWeSopChangeService;


    @Autowired
    private RabbitTemplate rabbitTemplate;


    @Autowired
    private RabbitMQSettingConfig rabbitMQSettingConfig;






    @Override
    public void sopExceptionEnd(String targetId) {
        this.update(
                WeSopExecuteTarget.builder()
                        .executeEndTime(new Date())
                        .executeState(SopExecuteStatus.SOP_STATUS_EXCEPTION.getType())
                        .build(),new LambdaQueryWrapper<WeSopExecuteTarget>()
                        .eq(WeSopExecuteTarget::getTargetId,targetId)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editSopExceptionEnd(Long sopBaseId, List<String> executeWeCustomerIdsOrGroupIds) {
        log.info("开始处理SOP编辑时的异常结束逻辑，SOP ID: {}, 当前满足条件的客户/群数量: {}",
                sopBaseId, executeWeCustomerIdsOrGroupIds != null ? executeWeCustomerIdsOrGroupIds.size() : 0);

        // 1. 查询当前SOP下所有执行中的目标
        List<WeSopExecuteTarget> allExecutingTargets = this.list(new LambdaQueryWrapper<WeSopExecuteTarget>()
                .eq(WeSopExecuteTarget::getSopBaseId, sopBaseId)
                .eq(WeSopExecuteTarget::getExecuteState, SopExecuteStatus.SOP_STATUS_ING.getType()));

        if (CollectionUtil.isEmpty(allExecutingTargets)) {
            log.info("SOP {} 下没有执行中的目标，无需处理", sopBaseId);
            return;
        }

        // 2. 分离出不再满足条件的目标
        List<WeSopExecuteTarget> targetsToEnd = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(executeWeCustomerIdsOrGroupIds)) {
            // 有满足条件的客户/群，找出不满足条件的
            targetsToEnd = allExecutingTargets.stream()
                    .filter(target -> !executeWeCustomerIdsOrGroupIds.contains(target.getTargetId()))
                    .collect(Collectors.toList());
        } else {
            // 没有满足条件的客户/群，所有执行中的都要结束
            targetsToEnd = allExecutingTargets;
        }

        if (CollectionUtil.isEmpty(targetsToEnd)) {
            log.info("SOP {} 下没有需要异常结束的目标", sopBaseId);
            return;
        }

        log.info("SOP {} 需要异常结束的目标数量: {}", sopBaseId, targetsToEnd.size());

        // 3. 设置目标为异常结束状态
        List<Long> targetIdsToEnd = targetsToEnd.stream().map(WeSopExecuteTarget::getId).collect(Collectors.toList());
        this.update(WeSopExecuteTarget.builder()
                .executeState(SopExecuteStatus.SOP_STATUS_EXCEPTION.getType())
                .executeEndTime(new Date())
                .build(), new LambdaQueryWrapper<WeSopExecuteTarget>()
                .in(WeSopExecuteTarget::getId, targetIdsToEnd));

        // 4. 删除相关的未执行附件（执行计划）
        long deletedAttachments = iWeSopExecuteTargetAttachmentsService.count(new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                .in(WeSopExecuteTargetAttachments::getExecuteTargetId, targetIdsToEnd)
                .eq(WeSopExecuteTargetAttachments::getExecuteState, 0)
                .eq(WeSopExecuteTargetAttachments::getDelFlag, 0));

        if (deletedAttachments > 0) {
            iWeSopExecuteTargetAttachmentsService.remove(new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                    .in(WeSopExecuteTargetAttachments::getExecuteTargetId, targetIdsToEnd)
                    .eq(WeSopExecuteTargetAttachments::getExecuteState, 0)
                    .eq(WeSopExecuteTargetAttachments::getDelFlag, 0));
            log.info("SOP {} 删除了 {} 个未执行的附件", sopBaseId, deletedAttachments);
        }

        log.info("完成SOP编辑时的异常结束处理，SOP ID: {}, 异常结束目标数量: {}", sopBaseId, targetsToEnd.size());
    }

    @Override
    public void earlyEndConditionsSop(){

        List<WeSopBase> weSopBases = iWeSopBaseService.list(
                new LambdaQueryWrapper<WeSopBase>()
                        .eq(WeSopBase::getSopState, 1)
                        .eq(WeSopBase::getDelFlag, Constants.COMMON_STATE)
                        .ne(WeSopBase::getEarlyEnd,0)
        );

        if(CollectionUtil.isNotEmpty(weSopBases)){
            weSopBases.stream().forEach(weSopBase -> {
                if(weSopBase.getBaseType().equals(1)){//客户sop,提前结束处理

                    WeSopExecuteEndVo endContent = (WeSopExecuteEndVo)weSopBase.getEndContent();
                    if(null != endContent){
                        //满足标签的客户
                        List<String> tagCustomerIds=new ArrayList<>();
                        WeSopExecuteEndVo.ExecuteTag executeTag = endContent.getExecuteTag();
                        if(null != executeTag && CollectionUtil.isNotEmpty(executeTag.getTagIds())){
                            List<String> weCustomerListIdsByApp
                                    = ((WeCustomerMapper) iWeCustomerService.getBaseMapper()).findWeCustomerListEuIds(WeCustomersQuery.builder()
                                    .tagIds(StringUtils.join(executeTag.getTagIds(), ","))
                                    .build());
                            if(CollectionUtil.isNotEmpty(weCustomerListIdsByApp)){
                                tagCustomerIds.addAll(weCustomerListIdsByApp);
                            }
                        }

                        //满足群条件的客户
                        List<String> goupCustomerIds=new ArrayList<>();
                        WeSopExecuteEndVo.JoinCustomerGroup
                                joinCustomerGroup = endContent.getJoinCustomerGroup();

                        if(null != joinCustomerGroup &&  StringUtils.isNotEmpty(joinCustomerGroup.getGroupCodeId())){

                            WeGroupCode weGroupCode = iWeGroupCodeService.getById(joinCustomerGroup.getGroupCodeId());
                            if(null != weGroupCode && StringUtils.isNotEmpty(weGroupCode.getChatIdList())){
                                List<WeGroupMember> weGroupMembers = iWeGroupMemberService.list(new LambdaQueryWrapper<WeGroupMember>()
                                        .in(WeGroupMember::getChatId, ListUtil.toList((weGroupCode.getChatIdList().split(","))))
                                );
                                if(CollectionUtil.isNotEmpty(weGroupMembers)){
                                    goupCustomerIds.addAll(
                                            weGroupMembers.stream().map(WeGroupMember::getUserId).collect(toList())
                                    );
                                }


                            }

                        }

                        //满足转接sop
                        List<String> sopCustomerIds=new ArrayList<>();
                        WeSopExecuteEndVo.ToChangeIntoOtherSop toChangeIntoOtherSop
                                = endContent.getToChangeIntoOtherSop();
                        if(null != toChangeIntoOtherSop && StringUtils.isNotEmpty(toChangeIntoOtherSop.getToChangeIntoSopId())){
                            List<WeSopExecuteTarget> executeTargets = this.list(new LambdaQueryWrapper<WeSopExecuteTarget>()
                                    .eq(WeSopExecuteTarget::getSopBaseId, toChangeIntoOtherSop.getToChangeIntoSopId()));
                            if(CollectionUtil.isNotEmpty(executeTargets)){
                                List<String> executeTargetIds=executeTargets.stream().map(WeSopExecuteTarget::getTargetId).collect(toList());
                                if(CollectionUtil.isNotEmpty(executeTargetIds)){
                                    sopCustomerIds.addAll(executeTargetIds);
                                }
                            }

                        }

                        //提前结束的客户
                        List<String> earlyEndCustomerIds=new ArrayList<>();

                        if(weSopBase.getEarlyEnd()==1){//任意条件

                            earlyEndCustomerIds.addAll(tagCustomerIds);
                            earlyEndCustomerIds.addAll(goupCustomerIds);
                            earlyEndCustomerIds.addAll(sopCustomerIds);

                        }else if(weSopBase.getEarlyEnd()==2){//全部条件

                            if(CollectionUtil.isNotEmpty(tagCustomerIds)&&CollectionUtil.isNotEmpty(goupCustomerIds)
                                    &&CollectionUtil.isNotEmpty(sopCustomerIds)){
                                 tagCustomerIds.retainAll(goupCustomerIds);
                                 tagCustomerIds.retainAll(sopCustomerIds);
                            }


                        }


                        if(CollectionUtil.isNotEmpty(earlyEndCustomerIds)){//提前结束的客户

                            //提前结束当前目标下的sop
                            this.update(WeSopExecuteTarget.builder()
                                    .executeState(SopExecuteStatus.SOP_STATUS_ADVANCE.getType())
                                    .build(), new LambdaQueryWrapper<WeSopExecuteTarget>()
                                    .eq(WeSopExecuteTarget::getExecuteState,SopExecuteStatus.SOP_STATUS_ING.getType())
                                    .eq(WeSopExecuteTarget::getSopBaseId,weSopBase.getId())
                                    .in(WeSopExecuteTarget::getTargetId,earlyEndCustomerIds)
                            );







                        }





                    }



                }else{//客群sop,提前结束处理
                    WeSopExecuteEndVo endContent = weSopBase.getEndContent();
                    if(null != endContent){
                        WeSopExecuteEndVo.ExecuteTag executeTag = endContent.getExecuteTag();
                        if(null != executeTag && CollectionUtil.isNotEmpty(executeTag.getTagIds())){
                            //筛选当前标签下对应的群
                            List<LinkGroupChatListVo> chatListVos = iWeGroupService.selectWeGroupListByApp(WeGroupChatQuery.builder()
                                    .tagIds(StringUtils.join(executeTag.getTagIds(), ","))
                                    .build());
                            if(CollectionUtil.isNotEmpty(chatListVos)){
                                //提前结束当前目标下的sop
                                this.update(WeSopExecuteTarget.builder()
                                                .executeState(SopExecuteStatus.SOP_STATUS_ADVANCE.getType())
                                        .build(), new LambdaQueryWrapper<WeSopExecuteTarget>()
                                        .eq(WeSopExecuteTarget::getExecuteState,SopExecuteStatus.SOP_STATUS_ING.getType())
                                        .eq(WeSopExecuteTarget::getSopBaseId,weSopBase.getId())
                                        .in(WeSopExecuteTarget::getTargetId,chatListVos.stream().map(LinkGroupChatListVo::getChatId).collect(toList()))
                                );
                            }
                        }
                    }
                }
            });


        }



    }


    @Override
    @Transactional
    public void sopExecuteEndAction(Long executeTargetId) {

        WeSopExecuteTarget executeTarget = this.getById(executeTargetId);
        if(null != executeTarget){

            //更新sop执行表状态
            executeTarget.setExecuteState(SopExecuteStatus.SOP_STATUS_COMMON.getType());
            executeTarget.setExecuteEndTime(new Date());
            //执行相关动作
            WeSopBase weSopBase
                    = iWeSopBaseService.getById(executeTarget.getSopBaseId());
            if(null != weSopBase){
                if(weSopBase.getBaseType()==1){//客户sop
                    WeSopExecuteEndVo endContent = weSopBase.getEndContent();
                    if(null != endContent) {//非提前结束sop
                        //标签行为
                        WeSopExecuteEndVo.ExecuteTag executeTag = endContent.getExecuteTag();
                        if(null != executeTag && CollectionUtil.isNotEmpty(executeTag.getTagIds())){
                            List<WeTag> weTags=new ArrayList<>();

                            executeTag.getTagIds().stream().forEach(tagId->{
                                weTags.add(
                                        WeTag.builder()
                                                .tagId(tagId)
                                                .build()
                                );
                            });

                            iWeCustomerService.makeLabel(WeMakeCustomerTag.builder()
                                    .externalUserid(executeTarget.getTargetId())
                                    .userId(executeTarget.getExecuteWeUserId())
                                    .isCompanyTag(true)
                                            .source(false)
                                    .addTag(weTags)
                                    .build());
                        }



                        //转入其他sop
                        WeSopExecuteEndVo.ToChangeIntoOtherSop toChangeIntoOtherSop = endContent.getToChangeIntoOtherSop();
                        if(null != toChangeIntoOtherSop && StringUtils.isNotEmpty(toChangeIntoOtherSop.getToChangeIntoSopId())){


                            WeSopChange weSopChange = WeSopChange.builder()
                                    .addUserId(executeTarget.getExecuteWeUserId())
                                    .sopBaseId(Long.parseLong(toChangeIntoOtherSop.getToChangeIntoSopId()))
                                    .externalUserid(executeTarget.getTargetId())
                                    .build();


                            //入库至其他sop表中（记录）
                           if( iWeSopChangeService.save(
                                   weSopChange
                           )){
                               //通知处理转入构建下一个群的计划
                               rabbitTemplate.convertAndSend(rabbitMQSettingConfig.getSopEx(), rabbitMQSettingConfig.getChnageWeCustomerSopRk(), JSONObject.toJSONString(weSopChange));

                           };


                        }


                    }




                }else if(weSopBase.getBaseType()==2){ //客群sop(对应的客群打标签)

                    WeSopExecuteEndVo endContent = weSopBase.getEndContent();
                    if(null != endContent && weSopBase.getEarlyEnd()==0){//非提前结束sop
                        WeSopExecuteEndVo.ExecuteTag executeTag = endContent.getExecuteTag();
                        if(null != executeTag && StringUtils.isNotEmpty(executeTag.getTagIds())){
                            weGroupTagRelService.makeGroupTag(
                                    WeMakeGroupTagQuery.builder()
                                            .chatId(executeTarget.getTargetId())
                                            .tagIds(executeTag.getTagIds())
                                            .build()
                            );
                        }

                    }
                }

            }


            this.updateById(
                    executeTarget
            );


        }








    }

    @Override
    public void builderCycleExecutionPlan() {
        List<WeSopBase> weSopBases = iWeSopBaseService.list(new LambdaQueryWrapper<WeSopBase>()
                .eq(WeSopBase::getSopState, 1)
                .eq(WeSopBase::getBusinessType, SopType.SOP_TYPE_ZQYX.getSopKey())
                .eq(WeSopBase::getDelFlag, Constants.COMMON_STATE));

        if(CollectionUtil.isNotEmpty(weSopBases)){
            weSopBases.stream().forEach(weSopBase -> {
                //构建客群sop执行计划
                iWeSopBaseService.builderExecuteGroupSopPlan(weSopBase
                        ,iWeSopBaseService.builderExecuteGroup(weSopBase,null),true,false);

            });
        }
    }

}
