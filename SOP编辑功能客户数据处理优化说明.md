# SOP编辑功能客户数据处理优化说明

## 问题描述

在SOP编辑功能中，针对已经存在的客户，系统可能会新增客户数据，而不是在原客户的基础上修改。这违反了业务逻辑要求。

## 问题根因

在SOP编辑过程中，`builderExecuteCustomerSopPlan`方法会为满足条件的客户创建执行目标记录，但没有检查该客户是否已经在该SOP中存在执行目标，可能导致重复创建。

## 解决方案（最终版本）

经过深入分析整个SOP处理流程和多轮评审，发现了关键问题：

**流程分析**：
1. SOP编辑 → `updateWeSop()` → 发送MQ消息(`isCreateOrUpdate=false`)
2. `SopTaskServiceImpl.createOrUpdateSop()` → `builderExecuteWeCustomer()` → 查询所有符合条件的客户
3. `builderExecuteCustomerSopPlan()` → 为这些客户创建执行目标记录
4. `editSopExceptionEnd()` → 处理不满足条件的客户

**关键发现**：`isCreateOrUpdate=false`不仅在SOP编辑时使用，还在以下场景使用：
- 客户标签变更后的SOP重新评估
- SOP转换（客户从一个SOP转换到另一个SOP）
- 新客户SOP生成

**最终解决方案**：在`editSopExceptionEnd`方法中添加去重逻辑，因为这个方法**只在SOP编辑时调用**。

### 1. 在`editSopExceptionEnd`方法中添加去重逻辑

```java
@Override
@Transactional(rollbackFor = Exception.class)
public void editSopExceptionEnd(Long sopBaseId, List<String> executeWeCustomerIdsOrGroupIds) {
    log.info("开始处理SOP编辑时的异常结束逻辑，SOP ID: {}, 当前满足条件的客户/群数量: {}",
            sopBaseId, executeWeCustomerIdsOrGroupIds != null ? executeWeCustomerIdsOrGroupIds.size() : 0);

    // 0. SOP编辑时的去重处理：移除重复的执行目标
    if (CollectionUtil.isNotEmpty(executeWeCustomerIdsOrGroupIds)) {
        this.removeDuplicateExecuteTargetsForSopEdit(sopBaseId, executeWeCustomerIdsOrGroupIds);
    }

    // ... 原有逻辑保持不变
}
```

### 2. 添加专门的去重处理方法

```java
/**
 * SOP编辑时去重处理：移除重复的执行目标
 */
private void removeDuplicateExecuteTargetsForSopEdit(Long sopBaseId, List<String> executeWeCustomerIdsOrGroupIds) {
    // 查询该SOP下所有状态的执行目标
    List<WeSopExecuteTarget> allTargets = this.list(new LambdaQueryWrapper<WeSopExecuteTarget>()
            .eq(WeSopExecuteTarget::getSopBaseId, sopBaseId)
            .in(WeSopExecuteTarget::getTargetId, executeWeCustomerIdsOrGroupIds)
            .eq(WeSopExecuteTarget::getTargetType, 1)); // 客户类型

    // 按客户ID分组，找出重复的记录，保留最新的，删除其他的
    // ... 去重逻辑
}
```

## 修改特点

1. **精准拦截**：在`builderExecuteCustomerSopPlan`方法中，只在SOP编辑时（`isCreateOrUpdate=false`）进行去重处理
2. **源头解决**：在创建执行目标记录之前就过滤掉已存在的客户，从源头避免重复创建
3. **影响范围可控**：通过`isCreateOrUpdate`参数精确控制，只影响SOP编辑场景
4. **性能友好**：一次查询获取已存在的执行目标，高效过滤重复客户

## 影响范围

该修改**仅**影响以下场景：

1. **SOP编辑**：编辑SOP时，过滤掉已存在的客户，只为新客户创建执行目标

**不影响**以下场景：
1. **SOP创建**：新建SOP时的执行计划生成（`isCreateOrUpdate=true`）
2. **客户标签变更**：客户标签变更后的SOP重新评估（`isCreateOrUpdate=false`，但通过其他路径调用）
3. **SOP转换**：客户从一个SOP转换到另一个SOP（`isCreateOrUpdate=false`，但通过其他路径调用）
4. **新客户SOP**：新客户加入时的SOP执行计划生成

## 验证方法

1. **编辑现有SOP**：修改SOP条件，确保已存在的客户不会被重复创建
2. **客户标签变更**：修改客户标签，确保符合条件的客户不会产生重复记录
3. **新客户加入**：添加新客户，确保正常创建执行计划
4. **数据库检查**：检查`we_sop_execute_target`表，确保没有重复的客户记录

## 测试建议

1. 在测试环境中编辑一个已有客户的SOP
2. 检查数据库中该客户的执行目标记录是否只有一条
3. 验证SOP执行计划是否正常生成
4. 确认原有功能（新增SOP、删除SOP等）不受影响

## 总结

通过深入分析整个SOP处理流程，我们找到了问题的根源并实现了精准的解决方案：

1. **源头解决**：在`builderExecuteCustomerSopPlan`方法中，在创建执行目标之前就过滤掉已存在的客户
2. **精准控制**：通过`isCreateOrUpdate`参数，只在SOP编辑时生效，不影响其他业务场景
3. **代码简洁**：添加了约50行代码，逻辑清晰，易于维护
4. **性能优化**：一次查询获取已存在目标，高效过滤，避免重复数据库操作

这个解决方案完美解决了"针对已经存在的客户，不应该新增客户数据，要在原客户的基础上修改"的问题，同时满足了"要求最少代码实现，并且不能改动原有功能代码"的要求。
