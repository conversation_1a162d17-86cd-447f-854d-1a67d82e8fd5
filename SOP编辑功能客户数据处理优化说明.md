# SOP编辑功能客户数据处理优化说明

## 问题描述

在SOP编辑功能中，针对已经存在的客户，系统可能会新增客户数据，而不是在原客户的基础上修改。这违反了业务逻辑要求。

## 问题根因

在`WeSopBaseServiceImpl.builderExecuteCustomerSopPlan`方法中，使用了`executeTargetService.saveOrUpdateBatch(weSopExecuteTargets)`方法，该方法会对客户执行目标进行"新增或更新"操作。但是对于已存在的客户，应该只进行更新操作，不应该创建新的客户数据。

## 解决方案

### 1. 修改`builderExecuteCustomerSopPlan`方法

在构建`WeSopExecuteTarget`对象之前，先查询该SOP下已存在的执行目标，避免重复创建客户数据：

```java
// 先查询该SOP下已存在的执行目标，避免重复创建客户数据
List<String> allTargetIds = executeWeCustomers.values().stream()
        .flatMap(List::stream)
        .map(WeCustomersVo::getExternalUserid)
        .collect(Collectors.toList());

Map<String, WeSopExecuteTarget> existingTargetsMap = new HashMap<>();
if (CollectionUtil.isNotEmpty(allTargetIds)) {
    List<WeSopExecuteTarget> existingTargets = executeTargetService.list(new LambdaQueryWrapper<WeSopExecuteTarget>()
            .eq(WeSopExecuteTarget::getSopBaseId, weSopBase.getId())
            .in(WeSopExecuteTarget::getTargetId, allTargetIds)
            .eq(WeSopExecuteTarget::getTargetType, 1)); // 客户类型
    existingTargetsMap = existingTargets.stream()
            .collect(Collectors.toMap(WeSopExecuteTarget::getTargetId, target -> target));
}
```

### 2. 区分新客户和已存在客户的处理逻辑

```java
executeWeCustomers.forEach((k, v) -> {
    v.stream().forEach(weCustomer -> {
        WeSopExecuteTarget existingTarget = existingTargetsMap.get(weCustomer.getExternalUserid());
        if (existingTarget != null) {
            // 对于已存在的客户，只更新必要字段，不创建新记录
            existingTarget.setAddCustomerOrCreateGoupTime(weCustomer.getFirstAddTime());
            existingTarget.setExecuteWeUserId(k);
            weSopExecuteTargets.add(existingTarget);
        } else {
            // 对于新客户，创建新的执行目标记录
            weSopExecuteTargets.add(
                    WeSopExecuteTarget.builder()
                            .targetId(weCustomer.getExternalUserid())
                            .targetType(1)
                            .addCustomerOrCreateGoupTime(weCustomer.getFirstAddTime())
                            .sopBaseId(weSopBase.getId())
                            .executeWeUserId(k)
                            .build()
            );
        }
    });
});
```

## 修改特点

1. **最少代码修改**：只修改了`builderExecuteCustomerSopPlan`方法，没有改动其他相关功能代码
2. **不影响原有功能**：保持了原有的业务逻辑，只是增加了去重检查
3. **向后兼容**：所有调用该方法的地方都能正常工作
4. **性能优化**：通过一次查询获取所有相关的执行目标，避免了多次数据库查询

## 影响范围

该修改会影响以下场景：

1. **SOP编辑**：编辑SOP时重新生成执行计划
2. **客户标签变更**：客户标签变更后的SOP重新评估
3. **SOP转换**：客户从一个SOP转换到另一个SOP
4. **新客户SOP**：新客户加入时的SOP执行计划生成

## 验证方法

1. **编辑现有SOP**：修改SOP条件，确保已存在的客户不会被重复创建
2. **客户标签变更**：修改客户标签，确保符合条件的客户不会产生重复记录
3. **新客户加入**：添加新客户，确保正常创建执行计划
4. **数据库检查**：检查`we_sop_execute_target`表，确保没有重复的客户记录

## 测试建议

1. 在测试环境中编辑一个已有客户的SOP
2. 检查数据库中该客户的执行目标记录是否只有一条
3. 验证SOP执行计划是否正常生成
4. 确认原有功能（新增SOP、删除SOP等）不受影响

## 总结

通过这次优化，确保了SOP编辑功能中客户数据的唯一性，避免了重复创建客户数据的问题，同时保持了系统的稳定性和性能。
