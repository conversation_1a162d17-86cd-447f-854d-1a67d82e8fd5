package org.scrm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.scrm.domain.customer.vo.WeCustomersVo;
import org.scrm.domain.sop.WeSopBase;
import org.scrm.domain.sop.WeSopExecuteTarget;
import org.scrm.service.IWeSopExecuteTargetService;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * SOP编辑功能测试类
 * 主要测试客户数据不重复创建的功能
 */
@ExtendWith(MockitoExtension.class)
class WeSopBaseServiceImplTest {

    @Mock
    private IWeSopExecuteTargetService executeTargetService;

    @InjectMocks
    private WeSopBaseServiceImpl weSopBaseService;

    private WeSopBase testSopBase;
    private Map<String, List<WeCustomersVo>> executeWeCustomers;
    private List<WeSopExecuteTarget> existingTargets;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testSopBase = new WeSopBase();
        testSopBase.setId(1L);
        testSopBase.setSopName("测试SOP");
        testSopBase.setBaseType(1); // 客户SOP

        // 准备客户数据
        WeCustomersVo customer1 = new WeCustomersVo();
        customer1.setExternalUserid("external_user_1");
        customer1.setFirstUserId("user_1");
        customer1.setFirstAddTime(new Date());

        WeCustomersVo customer2 = new WeCustomersVo();
        customer2.setExternalUserid("external_user_2");
        customer2.setFirstUserId("user_1");
        customer2.setFirstAddTime(new Date());

        executeWeCustomers = new HashMap<>();
        executeWeCustomers.put("user_1", Arrays.asList(customer1, customer2));

        // 准备已存在的执行目标数据（模拟customer1已存在）
        WeSopExecuteTarget existingTarget = new WeSopExecuteTarget();
        existingTarget.setId(100L);
        existingTarget.setSopBaseId(1L);
        existingTarget.setTargetId("external_user_1");
        existingTarget.setTargetType(1);
        existingTarget.setExecuteWeUserId("user_1");

        existingTargets = Arrays.asList(existingTarget);
    }

    @Test
    void testBuilderExecuteCustomerSopPlan_WithExistingCustomer() {
        // 模拟查询已存在的执行目标
        when(executeTargetService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(existingTargets);

        // 模拟saveOrUpdateBatch返回成功
        when(executeTargetService.saveOrUpdateBatch(any(List.class)))
                .thenReturn(true);

        // 执行测试方法
        weSopBaseService.builderExecuteCustomerSopPlan(testSopBase, executeWeCustomers, false, true);

        // 验证查询已存在目标的方法被调用
        verify(executeTargetService, times(1)).list(any(LambdaQueryWrapper.class));

        // 验证saveOrUpdateBatch被调用
        verify(executeTargetService, times(1)).saveOrUpdateBatch(any(List.class));

        // 验证传递给saveOrUpdateBatch的列表包含2个元素（1个更新，1个新增）
        verify(executeTargetService).saveOrUpdateBatch(argThat(targets -> {
            List<WeSopExecuteTarget> targetList = (List<WeSopExecuteTarget>) targets;
            return targetList.size() == 2;
        }));
    }

    @Test
    void testBuilderExecuteCustomerSopPlan_WithNoExistingCustomer() {
        // 模拟没有已存在的执行目标
        when(executeTargetService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // 模拟saveOrUpdateBatch返回成功
        when(executeTargetService.saveOrUpdateBatch(any(List.class)))
                .thenReturn(true);

        // 执行测试方法
        weSopBaseService.builderExecuteCustomerSopPlan(testSopBase, executeWeCustomers, false, true);

        // 验证查询已存在目标的方法被调用
        verify(executeTargetService, times(1)).list(any(LambdaQueryWrapper.class));

        // 验证saveOrUpdateBatch被调用
        verify(executeTargetService, times(1)).saveOrUpdateBatch(any(List.class));

        // 验证传递给saveOrUpdateBatch的列表包含2个新元素
        verify(executeTargetService).saveOrUpdateBatch(argThat(targets -> {
            List<WeSopExecuteTarget> targetList = (List<WeSopExecuteTarget>) targets;
            return targetList.size() == 2 && 
                   targetList.stream().allMatch(target -> target.getId() == null); // 新创建的对象ID为null
        }));
    }

    @Test
    void testBuilderExecuteCustomerSopPlan_EmptyCustomers() {
        // 测试空客户列表的情况
        Map<String, List<WeCustomersVo>> emptyCustomers = new HashMap<>();

        // 执行测试方法
        weSopBaseService.builderExecuteCustomerSopPlan(testSopBase, emptyCustomers, false, true);

        // 验证不会调用任何数据库操作
        verify(executeTargetService, never()).list(any(LambdaQueryWrapper.class));
        verify(executeTargetService, never()).saveOrUpdateBatch(any(List.class));
    }

    @Test
    void testBuilderExecuteCustomerSopPlan_CreateOrUpdateMode() {
        // 测试创建或更新模式（isCreateOrUpdate=true）
        when(executeTargetService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(existingTargets);

        when(executeTargetService.saveOrUpdateBatch(any(List.class)))
                .thenReturn(true);

        // 执行测试方法，isCreateOrUpdate=true
        weSopBaseService.builderExecuteCustomerSopPlan(testSopBase, executeWeCustomers, true, true);

        // 验证在创建或更新模式下，仍然会进行去重检查
        verify(executeTargetService, times(1)).list(any(LambdaQueryWrapper.class));
        verify(executeTargetService, times(1)).saveOrUpdateBatch(any(List.class));
    }
}
