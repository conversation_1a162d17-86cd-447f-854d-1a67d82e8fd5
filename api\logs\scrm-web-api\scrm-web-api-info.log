2025-07-26 09:02:39.992 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-26 09:02:40.018 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:02:40.018 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:02:40.089 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:02:40.089 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:02:40.089 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:02:40.090 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:02:40.090 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:02:42.111 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:02:42.113 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:02:42.288 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 160 ms. Found 0 Redis repository interfaces.
2025-07-26 09:02:42.863 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-26 09:02:43.351 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$54a15df6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:43.392 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:43.409 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:43.413 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:43.421 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:43.430 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:43.433 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:43.434 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:43.442 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:43.453 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:43.507 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:43.520 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:43.949 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:02:44.365 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-26 09:02:44.377 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-26 09:02:44.378 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 09:02:44.378 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 09:02:44.651 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-26 09:02:44.651 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4561 ms
2025-07-26 09:02:45.391 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:46.000 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:02:47.314 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-26 09:02:55.831 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:55.843 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:56.268 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:57.713 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:58.358 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 09:02:58.888 [redisson-netty-4-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:02:58.897 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:02:59.338 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:59.364 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:00.349 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:01.875 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:02.615 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:03.016 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:05.427 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:05.666 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:06.664 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:06.711 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:07.819 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:08.533 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:09.864 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:03:09.865 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:03:10.705 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:03:10.994 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:03:10.995 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:03:10.995 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:03:11.200 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-26 09:03:11.215 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-26 09:03:11.219 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:03:11.220 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:03:11.261 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-26 09:03:11.425 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 33.477 seconds (JVM running for 35.325)
2025-07-26 09:03:11.432 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:11.434 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:11.435 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:11.436 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:11.436 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:11.436 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:11.437 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:11.437 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:12.283 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:03:12.291 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:03:49.094 [http-nio-6091-exec-1] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 09:03:49.095 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-26 09:03:49.098 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-26 09:03:49.207 [http-nio-6091-exec-1] INFO  o.scrm.controller.WeIndexController - 可见范围客户数:[]
2025-07-26 09:03:49.602 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3d4e578a[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:03:50.011 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@328869c4[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:03:55.511 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:03:59.751 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:04:08.214 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:04:33.653 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [polling-resp] config changed. dataId=scrm-common.yml, group=DEFAULT_GROUP, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:04:33.653 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - get changedGroupKeys:[scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505]
2025-07-26 09:04:33.719 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [data-received] dataId=scrm-common.yml, group=DEFAULT_GROUP, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, md5=b0abac52cbc78c9c8caa15d5d5109373, content=scrm:
  baiduMapsAk: HMM2G1pfhLrrtjI2dgOvQtokNqazExUY
  h5Domain: https://wework.doha1000day.com/mob..., type=yaml
2025-07-26 09:04:33.720 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-context] dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373
2025-07-26 09:04:33.984 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:04:33.984 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:04:33.984 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:04:33.985 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:04:33.985 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:04:33.985 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:04:34.103 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[scrm-web-api] & group[DEFAULT_GROUP]
2025-07-26 09:04:34.172 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[scrm-web-api-dev.yml] & group[DEFAULT_GROUP]
2025-07-26 09:04:34.172 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-scrm-web-api-dev.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-web-api.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-web-api,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-common.yml,DEFAULT_GROUP'}]
2025-07-26 09:04:34.354 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.boot.SpringApplication - The following 1 profile is active: "dev"
2025-07-26 09:04:34.384 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.boot.SpringApplication - Started application in 0.657 seconds (JVM running for 118.285)
2025-07-26 09:04:34.504 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-error] dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@60745f78 tx={}
org.springframework.boot.context.properties.bind.BindException: Failed to bind properties under 'spring.datasource.druid' to javax.sql.DataSource
	at org.springframework.boot.context.properties.bind.Binder.handleBindError(Binder.java:384)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:344)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:329)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:259)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:246)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBinder.bind(ConfigurationPropertiesBinder.java:95)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.bind(ConfigurationPropertiesBindingPostProcessor.java:89)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.postProcessBeforeInitialization(ConfigurationPropertiesBindingPostProcessor.java:78)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:431)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.rebind(ConfigurationPropertiesRebinder.java:105)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.rebind(ConfigurationPropertiesRebinder.java:83)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.onApplicationEvent(ConfigurationPropertiesRebinder.java:138)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.onApplicationEvent(ConfigurationPropertiesRebinder.java:51)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:421)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:378)
	at org.springframework.cloud.context.refresh.ContextRefresher.refreshEnvironment(ContextRefresher.java:103)
	at org.springframework.cloud.context.refresh.ContextRefresher.refresh(ContextRefresher.java:94)
	at org.springframework.cloud.endpoint.event.RefreshEventListener.handle(RefreshEventListener.java:72)
	at org.springframework.cloud.endpoint.event.RefreshEventListener.onApplicationEvent(RefreshEventListener.java:61)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:421)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:378)
	at com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1.innerReceive(NacosContextRefresher.java:133)
	at com.alibaba.nacos.api.config.listener.AbstractSharedListener.receiveConfigInfo(AbstractSharedListener.java:40)
	at com.alibaba.nacos.client.config.impl.CacheData$1.run(CacheData.java:210)
	at com.alibaba.nacos.client.config.impl.CacheData.safeNotifyListener(CacheData.java:241)
	at com.alibaba.nacos.client.config.impl.CacheData.checkListenerMd5(CacheData.java:180)
	at com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable.run(ClientWorker.java:569)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.IllegalStateException: Unable to set value for property url
	at org.springframework.boot.context.properties.bind.JavaBeanBinder$BeanProperty.setValue(JavaBeanBinder.java:367)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:101)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:83)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:59)
	at org.springframework.boot.context.properties.bind.Binder.lambda$bindDataObject$5(Binder.java:473)
	at org.springframework.boot.context.properties.bind.Binder$Context.withIncreasedDepth(Binder.java:587)
	at org.springframework.boot.context.properties.bind.Binder$Context.withDataObject(Binder.java:573)
	at org.springframework.boot.context.properties.bind.Binder$Context.access$300(Binder.java:534)
	at org.springframework.boot.context.properties.bind.Binder.bindDataObject(Binder.java:471)
	at org.springframework.boot.context.properties.bind.Binder.bindObject(Binder.java:411)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:340)
	... 41 common frames omitted
Caused by: java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder$BeanProperty.setValue(JavaBeanBinder.java:364)
	... 51 common frames omitted
Caused by: java.lang.UnsupportedOperationException: null
	at com.alibaba.druid.pool.DruidAbstractDataSource.setUrl(DruidAbstractDataSource.java:1203)
	... 56 common frames omitted
2025-07-26 09:04:34.507 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-listener] time cost=787ms in ClientWorker, dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@60745f78 
2025-07-26 09:04:40.381 [Thread-79] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-26 09:04:40.382 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-26 09:04:40.383 [Thread-79] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-26 09:04:40.383 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-26 09:05:12.058 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-26 09:05:12.088 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:05:12.088 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:05:12.088 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:05:12.088 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:05:12.088 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:05:12.088 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:05:12.088 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:05:14.060 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:05:14.062 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:05:14.252 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 175 ms. Found 0 Redis repository interfaces.
2025-07-26 09:05:14.808 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-26 09:05:15.199 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$7ab6967b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:15.229 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:15.236 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:15.239 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:15.245 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:15.252 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:15.254 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:15.256 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:15.264 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:15.279 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:15.327 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:15.339 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:15.737 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:05:16.179 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-26 09:05:16.190 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-26 09:05:16.190 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 09:05:16.191 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 09:05:16.455 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-26 09:05:16.455 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4366 ms
2025-07-26 09:05:17.106 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:17.885 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:05:19.151 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-26 09:05:28.034 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:28.046 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:28.543 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:29.516 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:30.216 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 09:05:30.550 [redisson-netty-4-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:05:30.559 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:05:30.873 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:30.889 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:31.490 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:32.630 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:33.138 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:33.389 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:35.731 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:36.005 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:37.275 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:37.371 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:39.240 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:40.927 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:44.774 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:05:44.776 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:05:46.424 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:05:46.764 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:05:46.765 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:05:46.766 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:05:46.984 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-26 09:05:46.999 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-26 09:05:47.003 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:05:47.004 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:05:47.033 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-26 09:05:47.202 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 37.244 seconds (JVM running for 38.854)
2025-07-26 09:05:47.212 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:05:47.214 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:05:47.215 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:05:47.215 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:05:47.216 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:05:47.216 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:05:47.216 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:05:47.216 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:05:48.068 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:05:48.076 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:06:19.007 [http-nio-6091-exec-2] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 09:06:19.008 [http-nio-6091-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-26 09:06:19.011 [http-nio-6091-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-26 09:06:19.127 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:06:24.354 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:06:28.470 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@93bec19[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:06:28.891 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@93bec19[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:06:29.224 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@93bec19[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:06:50.706 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@25119084[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:06:50.783 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@25119084[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:06:50.842 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@25119084[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:06:54.974 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:06:57.379 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@69498b75[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:06:57.405 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@559f9d04[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:07:20.159 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@131a77e6[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:07:25.471 [http-nio-6091-exec-7] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [127.0.0.1:5672]
2025-07-26 09:07:25.516 [http-nio-6091-exec-7] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#54c9c09c:0/SimpleConnection@21ad28b1 [delegate=amqp://admin@127.0.0.1:5672/iyqueVhost, localPort= 52961]
2025-07-26 09:07:25.612 [http-nio-6091-exec-7] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948913009719263233 创建成功
2025-07-26 09:07:25.800 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:07:41.030 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:07:44.596 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:07:46.904 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@76b06c34[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:07:46.904 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6558242[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:07:56.293 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@426838b[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:08:08.635 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:08:11.679 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:08:13.350 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@33b4258e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:08:13.352 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5462ac89[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:08:15.235 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:08:21.218 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:08:42.590 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:08:57.240 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:08:58.648 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:09:00.160 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@146254c3[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:09:00.160 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@537cfd5b[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:09:34.080 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@768613c7[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:09:39.527 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948913572485808129 创建成功
2025-07-26 09:09:40.406 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:10:01.833 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@ebf4981[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:10:01.833 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@2bffb1cf[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:10:04.374 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:10:05.618 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1196c32a[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:10:05.618 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3ed98ac[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:10:09.475 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@17247b31[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:10:23.884 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:10:24.685 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@698d59f5[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:10:25.015 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@698d59f5[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:10:25.340 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@698d59f5[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:10:28.107 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:10:30.587 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:10:33.099 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@376b1589[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:10:33.099 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4b5bc7a9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:10:39.553 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7b7fc96c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:10:51.730 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:12:36.418 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:17:37.973 [Thread-87] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-26 09:17:37.973 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-26 09:17:37.973 [Thread-87] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-26 09:17:37.974 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-26 09:40:27.328 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-26 09:40:27.350 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:40:27.350 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:40:27.350 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:40:27.350 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:40:27.350 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:40:27.350 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:40:27.350 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:40:29.334 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:40:29.336 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:40:29.525 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 174 ms. Found 0 Redis repository interfaces.
2025-07-26 09:40:30.020 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-26 09:40:30.493 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$1624e421] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:30.527 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:30.535 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:30.538 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:30.546 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:30.556 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:30.559 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:30.560 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/288468653] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:30.571 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:30.584 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:30.634 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:30.645 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:40:31.092 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:40:31.521 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-26 09:40:31.529 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-26 09:40:31.529 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 09:40:31.529 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 09:40:31.783 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-26 09:40:31.783 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4433 ms
2025-07-26 09:40:32.378 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:33.049 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:40:34.449 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-26 09:40:41.912 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:41.921 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:42.236 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:42.993 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:43.494 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 09:40:43.780 [redisson-netty-4-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:40:43.788 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:40:44.057 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:44.069 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:44.578 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:45.628 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:46.031 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:46.267 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:47.916 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:48.099 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:48.861 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:48.903 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:49.896 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:50.484 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:51.530 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:40:51.531 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:40:52.396 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:40:52.597 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:40:52.597 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:40:52.598 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:40:52.847 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-26 09:40:52.857 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-26 09:40:52.860 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:40:52.861 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:40:52.893 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-26 09:40:53.076 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 27.798 seconds (JVM running for 29.446)
2025-07-26 09:40:53.082 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:40:53.084 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:40:53.085 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:40:53.085 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:40:53.085 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:40:53.086 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:40:53.086 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:40:53.086 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:40:53.917 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:40:53.922 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:41:09.947 [http-nio-6091-exec-1] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 09:41:09.947 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-26 09:41:09.952 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-26 09:41:10.097 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:41:15.469 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:41:17.242 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@385f9a5a[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:41:17.242 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1fab434b[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:41:18.875 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@59b054e1[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:41:19.447 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4f7ca901[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:41:19.815 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4f7ca901[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:41:20.169 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4f7ca901[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:41:28.322 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@77da0f1f[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:41:28.395 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@77da0f1f[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:41:28.464 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@77da0f1f[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:41:34.423 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:41:35.967 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@164023c2[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:41:35.967 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@f9d75c6[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:42:00.374 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@509f8140[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:42:05.285 [http-nio-6091-exec-7] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [127.0.0.1:5672]
2025-07-26 09:42:05.329 [http-nio-6091-exec-7] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#3f808ad9:0/SimpleConnection@4c9c3699 [delegate=amqp://admin@127.0.0.1:5672/iyqueVhost, localPort= 55300]
2025-07-26 09:42:05.419 [http-nio-6091-exec-7] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948921733280243714 创建成功
2025-07-26 09:42:05.603 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:42:40.221 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@2bef837f[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:42:40.221 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@769e0e6e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:42:41.757 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:42:46.399 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:42:52.580 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7913d22b[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:42:52.580 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@27de8839[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:42:54.775 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:42:57.326 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1af9b56[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:42:57.326 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3fd197a1[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:42:59.339 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@2f9404e8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:43:07.741 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:43:10.123 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:43:11.873 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@21e5c7c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:43:11.873 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7c36fca8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:43:15.146 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@45153c25[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:43:26.888 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:43:56.384 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:43:57.490 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@104b2536[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:43:57.490 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@774e45a2[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:43:59.201 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:44:20.238 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:44:22.844 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:44:24.322 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@74432c69[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:44:24.322 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4c65a01e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:44:39.382 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@62f63ae9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:44:45.105 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948922403609714690 创建成功
2025-07-26 09:44:45.529 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:45:00.057 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:45:02.157 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:45:03.429 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:45:04.314 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@625a4b79[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:45:04.315 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@55b291f6[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:45:27.852 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7affe391[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:45:32.986 [http-nio-6091-exec-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948922604726591490 创建成功
2025-07-26 09:45:34.112 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:45:42.425 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:45:43.563 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@b4a155a[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:45:43.563 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1f200f4d[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:45:46.729 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4b4cd571[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:45:55.153 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:45:57.413 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:45:58.814 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@395dec89[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:45:58.814 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3b1b2475[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:46:00.121 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@234475be[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:46:08.270 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 09:50:12.014 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-26 09:50:12.014 [Thread-69] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-26 09:50:12.014 [Thread-69] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-26 09:50:12.015 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-26 10:01:27.814 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-26 10:01:27.843 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 10:01:27.843 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 10:01:27.843 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 10:01:27.843 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 10:01:27.843 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 10:01:27.843 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 10:01:27.843 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 10:01:29.682 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 10:01:29.684 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 10:01:29.839 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 142 ms. Found 0 Redis repository interfaces.
2025-07-26 10:01:30.278 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-26 10:01:30.748 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$1dfc8c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:01:30.783 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:01:30.793 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:01:30.797 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:01:30.806 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:01:30.815 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:01:30.818 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:01:30.819 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:01:30.830 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:01:30.845 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:01:30.903 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:01:30.919 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:01:31.384 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 10:01:31.782 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-26 10:01:31.793 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-26 10:01:31.793 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 10:01:31.793 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 10:01:32.046 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-26 10:01:32.046 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4202 ms
2025-07-26 10:01:32.645 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:33.270 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 10:01:34.553 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-26 10:01:42.880 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:42.889 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:43.258 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:44.052 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:44.668 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 10:01:45.109 [redisson-netty-4-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 10:01:45.116 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 10:01:45.483 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:45.498 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:46.058 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:47.351 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:47.879 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:48.184 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:50.029 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:50.221 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:51.229 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:51.304 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:52.940 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:53.746 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:56.302 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 10:01:56.304 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 10:01:57.490 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 10:01:58.200 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 10:01:58.201 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 10:01:58.203 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 10:01:58.567 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-26 10:01:58.593 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-26 10:01:58.601 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 10:01:58.603 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 10:01:58.654 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-26 10:01:59.052 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 33.345 seconds (JVM running for 35.005)
2025-07-26 10:01:59.063 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 10:01:59.066 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 10:01:59.067 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 10:01:59.068 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 10:01:59.069 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 10:01:59.069 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-26 10:01:59.070 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 10:01:59.070 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 10:01:59.781 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 10:01:59.789 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 10:02:55.649 [http-nio-6091-exec-2] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 10:02:55.649 [http-nio-6091-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-26 10:02:55.672 [http-nio-6091-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 22 ms
2025-07-26 10:02:55.974 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 10:02:55.974 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 10:02:56.384 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - 客群条件:null
2025-07-26 10:02:56.385 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - json客群条件:null
2025-07-26 10:02:56.433 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - 客群条件:WeSopExecuteUserConditVo(executeUserCondit=WeSopExecuteUserConditVo.ExecuteUserCondit(change=true, weUserIds=[LuHongYan]), executeDeptCondit=WeSopExecuteUserConditVo.ExecuteDeptCondit(change=false, deptIds=[], posts=[]))
2025-07-26 10:02:56.554 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - json客群条件:{"executeUserCondit":{"change":true,"weUserIds":["LuHongYan"]},"executeDeptCondit":{"change":false,"deptIds":[],"posts":[]}}
2025-07-26 10:02:56.574 [http-nio-6091-exec-2] WARN  o.s.b.i.FeignRequestInterceptor - url:/system/user/findAllSysUser?weUserIds=LuHongYan  corpId 为null
2025-07-26 10:02:56.574 [http-nio-6091-exec-2] WARN  o.s.b.i.FeignRequestInterceptor - url:/system/user/findAllSysUser?weUserIds=LuHongYan  corpId 为null
2025-07-26 10:02:56.575 [http-nio-6091-exec-2] INFO  o.scrm.base.config.fegin.FeginLogger - [QwSysUserClient#findAllSysUser] ---> GET http://scrm-system/system/user/findAllSysUser?weUserIds=LuHongYan HTTP/1.1
2025-07-26 10:02:56.810 [http-nio-6091-exec-2] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 10:02:56.811 [http-nio-6091-exec-2] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 10:02:57.035 [http-nio-6091-exec-2] INFO  o.scrm.base.config.fegin.FeginLogger - [QwSysUserClient#findAllSysUser] request【GET http://scrm-system/system/user/findAllSysUser?weUserIds=LuHongYan HTTP/1.1】， body【】, response【{"msg":"操作成功","code":200,"data":[{"searchValue":null,"createBy":"超管","createById":"1","createTime":"2025-05-23 14:33:03","updateBy":"超管","updateById":"1","updateTime":"2025-07-07 11:29:32","remark":null,"params":{},"userId":"47","weUserId":"LuHongYan","deptId":"11","deptName":"AI产品2部","dept":null,"userName":"卢鸿演","userDepts":null,"position":"","phoneNumber":"15860932307","sex":"0","email":"","bizMail":"<EMAIL>","leader":"LaiBin","avatar":"https://wework.qpic.cn/wwpic3az/486234_45bRwKbhRn-unja_1727921066/0","thumbAvatar":null,"telephone":"","nickName":"","extAttr":"{\"attrs\":[{\"name\":\"岗位描述\",\"text\":{\"value\":\"\"},\"type\":0,\"value\":\"\"}]}","weUserStatus":1,"qrCode":"https://open.work.weixin.qq.com/wwopen/userQRCode?vcode=vc6f72477857d90911","externalProfile":"{\"external_attr\":[],\"external_corp_name\":\"\"}","externalPosition":null,"address":"","openUserid":null,"kfStatus":1,"dataScope":5,"jobNumber":null,"userType":null,"isAllocate":0,"isUserLeave":0,"dimissionTime":null,"salt":null,"status":"0","delFlag":0,"loginIp":"","loginDate":null,"isOpenChat":0,"roles":null,"roleIds":null,"roleId":null,"postIds":null,"companyName":null,"deptIds":null,"openDaily":null,"checkIsRoot":false,"account":null,"defaultAccount":0,"admin":false}]}】
2025-07-26 10:02:57.114 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - 客群条件:WeSopExecuteUserConditVo(executeUserCondit=WeSopExecuteUserConditVo.ExecuteUserCondit(change=true, weUserIds=[LuHongYan]), executeDeptCondit=WeSopExecuteUserConditVo.ExecuteDeptCondit(change=false, deptIds=[], posts=[]))
2025-07-26 10:02:57.114 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - json客群条件:{"executeUserCondit":{"change":true,"weUserIds":["LuHongYan"]},"executeDeptCondit":{"change":false,"deptIds":[],"posts":[]}}
2025-07-26 10:02:57.114 [http-nio-6091-exec-2] WARN  o.s.b.i.FeignRequestInterceptor - url:/system/user/findAllSysUser?weUserIds=LuHongYan  corpId 为null
2025-07-26 10:02:57.115 [http-nio-6091-exec-2] WARN  o.s.b.i.FeignRequestInterceptor - url:/system/user/findAllSysUser?weUserIds=LuHongYan  corpId 为null
2025-07-26 10:02:57.115 [http-nio-6091-exec-2] INFO  o.scrm.base.config.fegin.FeginLogger - [QwSysUserClient#findAllSysUser] ---> GET http://scrm-system/system/user/findAllSysUser?weUserIds=LuHongYan HTTP/1.1
2025-07-26 10:02:57.157 [http-nio-6091-exec-2] INFO  o.scrm.base.config.fegin.FeginLogger - [QwSysUserClient#findAllSysUser] request【GET http://scrm-system/system/user/findAllSysUser?weUserIds=LuHongYan HTTP/1.1】， body【】, response【{"msg":"操作成功","code":200,"data":[{"searchValue":null,"createBy":"超管","createById":"1","createTime":"2025-05-23 14:33:03","updateBy":"超管","updateById":"1","updateTime":"2025-07-07 11:29:32","remark":null,"params":{},"userId":"47","weUserId":"LuHongYan","deptId":"11","deptName":"AI产品2部","dept":null,"userName":"卢鸿演","userDepts":null,"position":"","phoneNumber":"15860932307","sex":"0","email":"","bizMail":"<EMAIL>","leader":"LaiBin","avatar":"https://wework.qpic.cn/wwpic3az/486234_45bRwKbhRn-unja_1727921066/0","thumbAvatar":null,"telephone":"","nickName":"","extAttr":"{\"attrs\":[{\"name\":\"岗位描述\",\"text\":{\"value\":\"\"},\"type\":0,\"value\":\"\"}]}","weUserStatus":1,"qrCode":"https://open.work.weixin.qq.com/wwopen/userQRCode?vcode=vc6f72477857d90911","externalProfile":"{\"external_attr\":[],\"external_corp_name\":\"\"}","externalPosition":null,"address":"","openUserid":null,"kfStatus":1,"dataScope":5,"jobNumber":null,"userType":null,"isAllocate":0,"isUserLeave":0,"dimissionTime":null,"salt":null,"status":"0","delFlag":0,"loginIp":"","loginDate":null,"isOpenChat":0,"roles":null,"roleIds":null,"roleId":null,"postIds":null,"companyName":null,"deptIds":null,"openDaily":null,"checkIsRoot":false,"account":null,"defaultAccount":0,"admin":false}]}】
2025-07-26 10:02:57.181 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - 客群条件:WeSopExecuteUserConditVo(executeUserCondit=WeSopExecuteUserConditVo.ExecuteUserCondit(change=true, weUserIds=[LuHongYan]), executeDeptCondit=WeSopExecuteUserConditVo.ExecuteDeptCondit(change=false, deptIds=[], posts=[]))
2025-07-26 10:02:57.181 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - json客群条件:{"executeUserCondit":{"change":true,"weUserIds":["LuHongYan"]},"executeDeptCondit":{"change":false,"deptIds":[],"posts":[]}}
2025-07-26 10:02:57.182 [http-nio-6091-exec-2] WARN  o.s.b.i.FeignRequestInterceptor - url:/system/user/findAllSysUser?weUserIds=LuHongYan  corpId 为null
2025-07-26 10:02:57.182 [http-nio-6091-exec-2] WARN  o.s.b.i.FeignRequestInterceptor - url:/system/user/findAllSysUser?weUserIds=LuHongYan  corpId 为null
2025-07-26 10:02:57.182 [http-nio-6091-exec-2] INFO  o.scrm.base.config.fegin.FeginLogger - [QwSysUserClient#findAllSysUser] ---> GET http://scrm-system/system/user/findAllSysUser?weUserIds=LuHongYan HTTP/1.1
2025-07-26 10:02:57.225 [http-nio-6091-exec-2] INFO  o.scrm.base.config.fegin.FeginLogger - [QwSysUserClient#findAllSysUser] request【GET http://scrm-system/system/user/findAllSysUser?weUserIds=LuHongYan HTTP/1.1】， body【】, response【{"msg":"操作成功","code":200,"data":[{"searchValue":null,"createBy":"超管","createById":"1","createTime":"2025-05-23 14:33:03","updateBy":"超管","updateById":"1","updateTime":"2025-07-07 11:29:32","remark":null,"params":{},"userId":"47","weUserId":"LuHongYan","deptId":"11","deptName":"AI产品2部","dept":null,"userName":"卢鸿演","userDepts":null,"position":"","phoneNumber":"15860932307","sex":"0","email":"","bizMail":"<EMAIL>","leader":"LaiBin","avatar":"https://wework.qpic.cn/wwpic3az/486234_45bRwKbhRn-unja_1727921066/0","thumbAvatar":null,"telephone":"","nickName":"","extAttr":"{\"attrs\":[{\"name\":\"岗位描述\",\"text\":{\"value\":\"\"},\"type\":0,\"value\":\"\"}]}","weUserStatus":1,"qrCode":"https://open.work.weixin.qq.com/wwopen/userQRCode?vcode=vc6f72477857d90911","externalProfile":"{\"external_attr\":[],\"external_corp_name\":\"\"}","externalPosition":null,"address":"","openUserid":null,"kfStatus":1,"dataScope":5,"jobNumber":null,"userType":null,"isAllocate":0,"isUserLeave":0,"dimissionTime":null,"salt":null,"status":"0","delFlag":0,"loginIp":"","loginDate":null,"isOpenChat":0,"roles":null,"roleIds":null,"roleId":null,"postIds":null,"companyName":null,"deptIds":null,"openDaily":null,"checkIsRoot":false,"account":null,"defaultAccount":0,"admin":false}]}】
2025-07-26 10:02:58.777 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 10:02:59.970 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 10:03:02.407 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 10:03:05.330 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 10:03:06.968 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@63bfd803[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 10:03:06.980 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5377f97c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 10:03:25.395 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@af50b32[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 10:03:29.436 [http-nio-6091-exec-1] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [127.0.0.1:5672]
2025-07-26 10:03:29.472 [http-nio-6091-exec-1] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#626edbe0:0/SimpleConnection@392c5977 [delegate=amqp://admin@127.0.0.1:5672/iyqueVhost, localPort= 57547]
2025-07-26 10:03:29.564 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948927119542575106 创建成功
2025-07-26 10:03:29.730 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 10:04:18.703 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 10:04:20.095 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@21778bf7[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 10:04:20.095 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@71aadc83[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 10:04:22.208 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5a65e822[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 10:04:30.380 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 10:04:32.748 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 10:04:34.251 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@26a5cda0[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 10:04:34.251 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@29707fbf[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 10:04:36.033 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=*************, expireTime=*************, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@28101b40[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 10:04:42.535 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-26 10:06:44.218 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-26 10:06:44.218 [Thread-77] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-26 10:06:44.219 [Thread-77] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-26 10:06:44.219 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
