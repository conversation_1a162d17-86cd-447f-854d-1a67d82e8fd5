
c9112356869aa162564d85435443f75ff264495a	{"key":"{\"terser\":\"4.8.1\",\"node_version\":\"v18.20.8\",\"terser-webpack-plugin\":\"1.4.6\",\"terser-webpack-plugin-options\":{\"test\":new RegExp(\"\\\\.m?js(\\\\?.*)?$\", \"i\"),\"chunkFilter\":() => true,\"warningsFilter\":() => true,\"extractComments\":false,\"sourceMap\":false,\"cache\":true,\"cacheKeys\":defaultCacheKeys => defaultCacheKeys,\"parallel\":true,\"include\":undefined,\"exclude\":undefined,\"minify\":undefined,\"terserOptions\":{\"output\":{\"comments\":new RegExp(\"^\\\\**!|@preserve|@license|@cc_on\", \"i\")},\"compress\":{\"arrows\":false,\"collapse_vars\":false,\"comparisons\":false,\"computed_props\":false,\"hoist_funs\":false,\"hoist_props\":false,\"hoist_vars\":false,\"inline\":false,\"loops\":false,\"negate_iife\":false,\"properties\":false,\"reduce_funcs\":false,\"reduce_vars\":false,\"switches\":false,\"toplevel\":false,\"typeofs\":false,\"booleans\":true,\"if_return\":true,\"sequences\":true,\"unused\":true,\"conditionals\":true,\"dead_code\":true,\"evaluate\":true,\"drop_console\":false,\"drop_debugger\":true},\"mangle\":{\"safari10\":true}}},\"hash\":\"e6fea7078ec188afc6033b2a457956bd\"}","integrity":"sha512-FWEXsPolRGLEDEUV9WXM1wfaZSjtSHo1nWIAeXQpXLh8i2A8m9VKKMoWRkg+fVPHvetEAQBuqbhJLlxh2G9crA==","time":1753490030535,"size":662874}