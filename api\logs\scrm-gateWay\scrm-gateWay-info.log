2025-07-26 09:01:48.644 [main] INFO  org.scrm.ScrmGatewayApplication - The following 1 profile is active: "dev"
2025-07-26 09:01:48.660 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:01:48.660 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:01:48.661 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:01:48.661 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:01:48.661 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:01:48.661 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:01:48.661 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:01:49.502 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:01:49.504 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:01:49.550 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-07-26 09:01:49.747 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[org.scrm]' package. Please check your configuration.
2025-07-26 09:01:49.821 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=24301223-7113-33b5-b484-079c86d537e1
2025-07-26 09:01:50.128 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.136 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.138 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.144 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.150 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.152 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.153 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$465/495885630] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.161 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.169 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.218 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.232 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.309 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:01:50.314 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.316 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.317 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:52.037 [main] INFO  o.s.g.s.GatewayDynamicRouteService - gateway route init...
2025-07-26 09:01:52.616 [main] INFO  o.s.g.s.GatewayDynamicRouteService - 获取网关当前配置:
[{
	"id": "scrm-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/auth/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"name": "ValidateCodeFilter"
	}, {
		"name": "CacheRequestFilter"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-auth-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/system/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-wechat-api",
	"order": 2,
	"predicates": [{
		"args": {
			"pattern": "/wecom/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"args": {
			"_genkey_0": "1"
		},
		"name": "StripPrefix"
	}],
	"uri": "lb://scrm-wechat-api"
}, {
	"id": "scrm-web-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/open/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-web-api"
}, {
	"id": "scrm-file",
	"order": 4,
	"predicates": [{
		"args": {
			"pattern": "/file/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-file"
}, {
	"id": "scrm-auth-common",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/common/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-mobile-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/wx-api/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-mobile-api"
}, {
	"id": "scrm-open-ai",
	"order": 6,
	"predicates": [{
		"args": {
			"pattern": "/ai/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-open-ai"
}]
2025-07-26 09:01:52.631 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-system', predicates=[PredicateDefinition{name='Path', args={pattern=/auth/**}}], filters=[FilterDefinition{name='ValidateCodeFilter', args={}}, FilterDefinition{name='CacheRequestFilter', args={}}], uri=lb://scrm-system, order=0, metadata={}}
2025-07-26 09:01:53.833 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-26 09:01:53.953 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:01:53.953 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:01:53.954 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:01:54.133 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-system', predicates=[PredicateDefinition{name='Path', args={pattern=/system/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-26 09:01:54.141 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-wechat-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wecom/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://scrm-wechat-api, order=2, metadata={}}
2025-07-26 09:01:54.150 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-web-api', predicates=[PredicateDefinition{name='Path', args={pattern=/open/**}}], filters=[], uri=lb://scrm-web-api, order=3, metadata={}}
2025-07-26 09:01:54.158 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-file', predicates=[PredicateDefinition{name='Path', args={pattern=/file/**}}], filters=[], uri=lb://scrm-file, order=4, metadata={}}
2025-07-26 09:01:54.165 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-common', predicates=[PredicateDefinition{name='Path', args={pattern=/common/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-26 09:01:54.174 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-mobile-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wx-api/**}}], filters=[], uri=lb://scrm-mobile-api, order=3, metadata={}}
2025-07-26 09:01:54.180 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-open-ai', predicates=[PredicateDefinition{name='Path', args={pattern=/ai/**}}], filters=[], uri=lb://scrm-open-ai, order=6, metadata={}}
2025-07-26 09:01:54.189 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] gateway-router+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:01:54.191 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=gateway-router, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:01:54.751 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -**********
2025-07-26 09:01:55.018 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-07-26 09:01:55.181 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:01:56.382 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-26 09:01:56.923 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:01:56.925 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:01:57.853 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:01:58.050 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 6180
2025-07-26 09:01:58.324 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6180, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-gateWay', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:01:58.325 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:01:58.361 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-gateWay **************:6180 register finished
2025-07-26 09:01:58.412 [main] INFO  org.scrm.ScrmGatewayApplication - Started ScrmGatewayApplication in 11.683 seconds (JVM running for 13.081)
2025-07-26 09:01:58.415 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:01:58.415 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:01:58.415 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:01:58.416 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:01:58.416 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:01:58.417 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:01:58.417 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:01:58.417 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:01:58.455 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:01:58.460 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:01:59.067 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:01:59.070 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:28.144 [boundedElastic-3] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:28.145 [boundedElastic-3] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:28.161 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:28.163 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:28.163 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:28.165 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:36.543 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753491816543-8376
2025-07-26 09:03:36.896 [reactor-http-nio-2] ERROR org.scrm.gateway.filter.AuthFilter - [鉴权异常处理]请求路径:/system/user/getInfo,msg:登录状态已过期
2025-07-26 09:03:46.907 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/auth/login 业务请求traceId:iyque-1753491826907-7051
2025-07-26 09:03:48.021 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753491828021-2928
2025-07-26 09:03:48.425 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753491828425-7549
2025-07-26 09:03:48.426 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753491828426-8767
2025-07-26 09:03:48.936 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/getWeIndex 业务请求traceId:iyque-1753491828936-5748
2025-07-26 09:03:49.534 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/trajectory/findCompanyDynamics 业务请求traceId:iyque-1753491829534-4730
2025-07-26 09:03:49.542 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/getRealCustomerCnt 业务请求traceId:iyque-1753491829542-4776
2025-07-26 09:03:49.546 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/operation/group/member/getGroupMemberRealNoPageCnt 业务请求traceId:iyque-1753491829546-3020
2025-07-26 09:03:49.990 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/trajectory/findCompanyDynamics 业务请求traceId:iyque-1753491829990-4354
2025-07-26 09:03:55.413 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753491835413-1415
2025-07-26 09:03:59.661 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateSopState 业务请求traceId:iyque-1753491839661-968
2025-07-26 09:03:59.748 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753491839748-5842
2025-07-26 09:04:07.989 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/1948650536413155330 业务请求traceId:iyque-1753491847989-3438
2025-07-26 09:04:08.209 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753491848209-8589
2025-07-26 09:04:33.659 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [polling-resp] config changed. dataId=scrm-common.yml, group=DEFAULT_GROUP, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:04:33.659 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - get changedGroupKeys:[scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505]
2025-07-26 09:04:33.735 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [data-received] dataId=scrm-common.yml, group=DEFAULT_GROUP, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, md5=b0abac52cbc78c9c8caa15d5d5109373, content=scrm:
  baiduMapsAk: HMM2G1pfhLrrtjI2dgOvQtokNqazExUY
  h5Domain: https://wework.doha1000day.com/mob..., type=yaml
2025-07-26 09:04:33.735 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-context] dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373
2025-07-26 09:04:33.992 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:04:33.992 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:04:33.992 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:04:33.992 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:04:33.992 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:04:33.992 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:04:34.107 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[scrm-gateWay] & group[DEFAULT_GROUP]
2025-07-26 09:04:34.184 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[scrm-gateWay-dev.yml] & group[DEFAULT_GROUP]
2025-07-26 09:04:34.184 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-scrm-gateWay-dev.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-gateWay.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-gateWay,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-common.yml,DEFAULT_GROUP'}]
2025-07-26 09:04:34.342 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.boot.SpringApplication - The following 1 profile is active: "dev"
2025-07-26 09:04:34.371 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.boot.SpringApplication - Started application in 0.629 seconds (JVM running for 169.04)
2025-07-26 09:04:34.505 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-error] dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@2dbf6f90 tx={}
org.springframework.boot.context.properties.bind.BindException: Failed to bind properties under 'spring.datasource.druid' to javax.sql.DataSource
	at org.springframework.boot.context.properties.bind.Binder.handleBindError(Binder.java:384)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:344)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:329)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:259)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:246)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBinder.bind(ConfigurationPropertiesBinder.java:95)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.bind(ConfigurationPropertiesBindingPostProcessor.java:89)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.postProcessBeforeInitialization(ConfigurationPropertiesBindingPostProcessor.java:78)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:431)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.rebind(ConfigurationPropertiesRebinder.java:105)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.rebind(ConfigurationPropertiesRebinder.java:83)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.onApplicationEvent(ConfigurationPropertiesRebinder.java:138)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.onApplicationEvent(ConfigurationPropertiesRebinder.java:51)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:421)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:378)
	at org.springframework.cloud.context.refresh.ContextRefresher.refreshEnvironment(ContextRefresher.java:103)
	at org.springframework.cloud.context.refresh.ContextRefresher.refresh(ContextRefresher.java:94)
	at org.springframework.cloud.endpoint.event.RefreshEventListener.handle(RefreshEventListener.java:72)
	at org.springframework.cloud.endpoint.event.RefreshEventListener.onApplicationEvent(RefreshEventListener.java:61)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:421)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:378)
	at com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1.innerReceive(NacosContextRefresher.java:133)
	at com.alibaba.nacos.api.config.listener.AbstractSharedListener.receiveConfigInfo(AbstractSharedListener.java:40)
	at com.alibaba.nacos.client.config.impl.CacheData$1.run(CacheData.java:210)
	at com.alibaba.nacos.client.config.impl.CacheData.safeNotifyListener(CacheData.java:241)
	at com.alibaba.nacos.client.config.impl.CacheData.checkListenerMd5(CacheData.java:180)
	at com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable.run(ClientWorker.java:569)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.IllegalStateException: Unable to set value for property url
	at org.springframework.boot.context.properties.bind.JavaBeanBinder$BeanProperty.setValue(JavaBeanBinder.java:367)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:101)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:83)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:59)
	at org.springframework.boot.context.properties.bind.Binder.lambda$bindDataObject$5(Binder.java:473)
	at org.springframework.boot.context.properties.bind.Binder$Context.withIncreasedDepth(Binder.java:587)
	at org.springframework.boot.context.properties.bind.Binder$Context.withDataObject(Binder.java:573)
	at org.springframework.boot.context.properties.bind.Binder$Context.access$300(Binder.java:534)
	at org.springframework.boot.context.properties.bind.Binder.bindDataObject(Binder.java:471)
	at org.springframework.boot.context.properties.bind.Binder.bindObject(Binder.java:411)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:340)
	... 41 common frames omitted
Caused by: java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder$BeanProperty.setValue(JavaBeanBinder.java:364)
	... 51 common frames omitted
Caused by: java.lang.UnsupportedOperationException: null
	at com.alibaba.druid.pool.DruidAbstractDataSource.setUrl(DruidAbstractDataSource.java:1203)
	... 56 common frames omitted
2025-07-26 09:04:34.506 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-listener] time cost=771ms in ClientWorker, dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@2dbf6f90 
2025-07-26 09:04:40.253 [Thread-31] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-26 09:04:40.253 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-26 09:04:40.259 [Thread-31] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-26 09:04:40.260 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-26 09:04:40.276 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-26 09:04:40.388 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-26 09:04:40.388 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 09:04:40.397 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-gateWay:**************:6180 from beat map.
2025-07-26 09:04:40.397 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-26 09:04:40.433 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 09:04:40.433 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-07-26 09:04:49.303 [main] INFO  org.scrm.ScrmGatewayApplication - The following 1 profile is active: "dev"
2025-07-26 09:04:49.319 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:04:49.319 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:04:49.319 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:04:49.319 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:04:49.319 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:04:49.319 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:04:49.319 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:04:50.093 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:04:50.095 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:04:50.136 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2025-07-26 09:04:50.334 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[org.scrm]' package. Please check your configuration.
2025-07-26 09:04:50.402 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=24301223-7113-33b5-b484-079c86d537e1
2025-07-26 09:04:50.663 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:04:50.670 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:04:50.672 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:04:50.677 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:04:50.683 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:04:50.685 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:04:50.686 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$465/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:04:50.691 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:04:50.699 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:04:50.741 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:04:50.752 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:04:50.835 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:04:50.839 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:04:50.840 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:04:50.841 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:04:51.920 [main] INFO  o.s.g.s.GatewayDynamicRouteService - gateway route init...
2025-07-26 09:04:52.403 [main] INFO  o.s.g.s.GatewayDynamicRouteService - 获取网关当前配置:
[{
	"id": "scrm-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/auth/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"name": "ValidateCodeFilter"
	}, {
		"name": "CacheRequestFilter"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-auth-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/system/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-wechat-api",
	"order": 2,
	"predicates": [{
		"args": {
			"pattern": "/wecom/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"args": {
			"_genkey_0": "1"
		},
		"name": "StripPrefix"
	}],
	"uri": "lb://scrm-wechat-api"
}, {
	"id": "scrm-web-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/open/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-web-api"
}, {
	"id": "scrm-file",
	"order": 4,
	"predicates": [{
		"args": {
			"pattern": "/file/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-file"
}, {
	"id": "scrm-auth-common",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/common/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-mobile-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/wx-api/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-mobile-api"
}, {
	"id": "scrm-open-ai",
	"order": 6,
	"predicates": [{
		"args": {
			"pattern": "/ai/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-open-ai"
}]
2025-07-26 09:04:52.468 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-system', predicates=[PredicateDefinition{name='Path', args={pattern=/auth/**}}], filters=[FilterDefinition{name='ValidateCodeFilter', args={}}, FilterDefinition{name='CacheRequestFilter', args={}}], uri=lb://scrm-system, order=0, metadata={}}
2025-07-26 09:04:53.673 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-07-26 09:04:53.673 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-07-26 09:04:53.673 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-07-26 09:04:53.673 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-07-26 09:04:53.673 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-07-26 09:04:53.674 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-07-26 09:04:53.674 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-07-26 09:04:53.674 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-07-26 09:04:53.674 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-07-26 09:04:53.674 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-07-26 09:04:53.674 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-26 09:04:53.674 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-26 09:04:53.674 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-07-26 09:04:53.674 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-26 09:04:53.823 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:04:53.824 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:04:53.824 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:04:54.113 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:04:54.113 [boundedElastic-3] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:04:54.118 [boundedElastic-3] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:04:54.118 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:04:54.128 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-system', predicates=[PredicateDefinition{name='Path', args={pattern=/system/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-26 09:04:54.135 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-wechat-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wecom/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://scrm-wechat-api, order=2, metadata={}}
2025-07-26 09:04:54.140 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-web-api', predicates=[PredicateDefinition{name='Path', args={pattern=/open/**}}], filters=[], uri=lb://scrm-web-api, order=3, metadata={}}
2025-07-26 09:04:54.146 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-file', predicates=[PredicateDefinition{name='Path', args={pattern=/file/**}}], filters=[], uri=lb://scrm-file, order=4, metadata={}}
2025-07-26 09:04:54.152 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-common', predicates=[PredicateDefinition{name='Path', args={pattern=/common/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-26 09:04:54.157 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-mobile-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wx-api/**}}], filters=[], uri=lb://scrm-mobile-api, order=3, metadata={}}
2025-07-26 09:04:54.163 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-open-ai', predicates=[PredicateDefinition{name='Path', args={pattern=/ai/**}}], filters=[], uri=lb://scrm-open-ai, order=6, metadata={}}
2025-07-26 09:04:54.172 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] gateway-router+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:04:54.174 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=gateway-router, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:04:54.694 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -**********
2025-07-26 09:04:54.905 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-07-26 09:04:55.063 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:04:56.249 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-26 09:04:56.714 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:04:56.715 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:04:57.459 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:04:57.638 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 6180
2025-07-26 09:04:57.902 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6180, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-gateWay', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:04:57.903 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:04:57.941 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-gateWay **************:6180 register finished
2025-07-26 09:04:58.043 [main] INFO  org.scrm.ScrmGatewayApplication - Started ScrmGatewayApplication in 10.645 seconds (JVM running for 11.784)
2025-07-26 09:04:58.047 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:04:58.048 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:04:58.048 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:04:58.048 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:04:58.050 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:04:58.050 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:04:58.051 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:04:58.051 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:04:58.653 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:04:58.654 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:05:01.251 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:05:01.252 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:05:05.216 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - modified ips(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:05:05.218 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:05:05.221 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - modified ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:05:05.222 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:05:15.274 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:05:15.275 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@scrm-web-api -> []
2025-07-26 09:05:15.277 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:05:15.279 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@scrm-task -> []
2025-07-26 09:05:57.374 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:05:57.383 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:06:15.280 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753491975280-7988
2025-07-26 09:06:16.144 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753491976144-2704
2025-07-26 09:06:16.145 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753491976145-6646
2025-07-26 09:06:16.545 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753491976545-9337
2025-07-26 09:06:16.590 [boundedElastic-6] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: scrm-web-api
2025-07-26 09:06:16.619 [boundedElastic-6] ERROR o.s.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/open/sop/findWeSopLists,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for scrm-web-api"
2025-07-26 09:06:17.517 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:06:17.518 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:06:17.518 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:06:17.520 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:06:18.500 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753491978500-7948
2025-07-26 09:06:18.643 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753491978643-7170
2025-07-26 09:06:18.643 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753491978643-3200
2025-07-26 09:06:18.932 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753491978932-9786
2025-07-26 09:06:24.158 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/1948638494602948609 业务请求traceId:iyque-1753491984158-9186
2025-07-26 09:06:24.350 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753491984350-9841
2025-07-26 09:06:28.388 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753491988388-3315
2025-07-26 09:06:28.389 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753491988389-2340
2025-07-26 09:06:28.389 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findCustomerAddWay 业务请求traceId:iyque-1753491988389-1176
2025-07-26 09:06:28.545 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753491988545-6402
2025-07-26 09:06:30.581 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/dept/list 业务请求traceId:iyque-1753491990581-2539
2025-07-26 09:06:46.895 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/findUserList 业务请求traceId:iyque-1753492006895-1387
2025-07-26 09:06:50.699 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753492010699-3697
2025-07-26 09:06:54.969 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492014968-8988
2025-07-26 09:06:57.359 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753492017359-4069
2025-07-26 09:06:57.359 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753492017359-6409
2025-07-26 09:07:08.626 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753492028626-6264
2025-07-26 09:07:09.167 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/dept/list 业务请求traceId:iyque-1753492029167-7932
2025-07-26 09:07:12.164 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/findUserList 业务请求traceId:iyque-1753492032164-578
2025-07-26 09:07:16.242 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753492036242-824
2025-07-26 09:07:20.152 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753492040152-5261
2025-07-26 09:07:25.159 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/createWeSop 业务请求traceId:iyque-1753492045159-9088
2025-07-26 09:07:25.796 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492045796-6015
2025-07-26 09:07:33.415 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948913009719263233 业务请求traceId:iyque-1753492053415-3910
2025-07-26 09:07:33.415 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailTab/1948913009719263233 业务请求traceId:iyque-1753492053415-2827
2025-07-26 09:07:33.415 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753492053415-2637
2025-07-26 09:07:36.874 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findCustomerExecuteContent 业务请求traceId:iyque-1753492056874-2141
2025-07-26 09:07:41.026 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492061026-3966
2025-07-26 09:07:44.542 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateSopState 业务请求traceId:iyque-1753492064542-8738
2025-07-26 09:07:44.593 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492064593-9212
2025-07-26 09:07:46.897 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948913009719263233 业务请求traceId:iyque-1753492066897-7497
2025-07-26 09:07:46.897 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753492066897-2108
2025-07-26 09:07:46.897 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753492066897-3938
2025-07-26 09:07:47.065 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753492067065-3348
2025-07-26 09:07:56.287 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753492076287-1908
2025-07-26 09:08:08.080 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateWeSop 业务请求traceId:iyque-1753492088080-2500
2025-07-26 09:08:08.631 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492088630-438
2025-07-26 09:08:11.628 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateSopState 业务请求traceId:iyque-1753492091628-3807
2025-07-26 09:08:11.676 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492091676-4536
2025-07-26 09:08:13.343 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948913009719263233 业务请求traceId:iyque-1753492093343-3060
2025-07-26 09:08:13.343 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753492093343-6606
2025-07-26 09:08:13.343 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753492093343-8981
2025-07-26 09:08:13.595 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753492093595-181
2025-07-26 09:08:15.232 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492095232-8241
2025-07-26 09:08:16.556 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753492096556-3823
2025-07-26 09:08:16.557 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948913009719263233 业务请求traceId:iyque-1753492096557-9426
2025-07-26 09:08:16.557 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailTab/1948913009719263233 业务请求traceId:iyque-1753492096557-9073
2025-07-26 09:08:21.214 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492101214-8096
2025-07-26 09:08:23.746 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753492103746-6696
2025-07-26 09:08:23.746 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948913009719263233 业务请求traceId:iyque-1753492103746-4728
2025-07-26 09:08:23.746 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailTab/1948913009719263233 业务请求traceId:iyque-1753492103746-109
2025-07-26 09:08:24.442 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findCustomerExecuteContent 业务请求traceId:iyque-1753492104442-6217
2025-07-26 09:08:26.802 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findCustomerExecuteContent 业务请求traceId:iyque-1753492106802-9512
2025-07-26 09:08:30.084 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findCustomerExecuteContent 业务请求traceId:iyque-1753492110084-1140
2025-07-26 09:08:32.483 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findCustomerExecuteContent 业务请求traceId:iyque-1753492112483-9709
2025-07-26 09:08:42.587 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492122587-7315
2025-07-26 09:08:57.188 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateSopState 业务请求traceId:iyque-1753492137187-905
2025-07-26 09:08:57.238 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492137238-7987
2025-07-26 09:08:58.470 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/1948913009719263233 业务请求traceId:iyque-1753492138470-7723
2025-07-26 09:08:58.645 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492138645-7427
2025-07-26 09:09:00.153 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753492140153-3321
2025-07-26 09:09:00.153 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753492140153-324
2025-07-26 09:09:21.981 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753492161981-3977
2025-07-26 09:09:22.895 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/dept/list 业务请求traceId:iyque-1753492162895-3582
2025-07-26 09:09:26.805 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/findUserList 业务请求traceId:iyque-1753492166805-5159
2025-07-26 09:09:30.363 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753492170363-7181
2025-07-26 09:09:34.074 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753492174074-2953
2025-07-26 09:09:39.382 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/createWeSop 业务请求traceId:iyque-1753492179382-3343
2025-07-26 09:09:40.399 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492180399-6636
2025-07-26 09:10:01.821 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948913572485808129 业务请求traceId:iyque-1753492201821-2549
2025-07-26 09:10:01.826 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753492201826-5888
2025-07-26 09:10:01.826 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753492201826-4691
2025-07-26 09:10:01.979 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753492201979-4093
2025-07-26 09:10:04.370 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492204370-9574
2025-07-26 09:10:05.612 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948913572485808129 业务请求traceId:iyque-1753492205612-189
2025-07-26 09:10:05.612 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753492205612-4194
2025-07-26 09:10:05.612 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753492205612-3446
2025-07-26 09:10:05.786 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753492205786-1621
2025-07-26 09:10:09.469 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753492209469-3749
2025-07-26 09:10:19.774 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateWeSop 业务请求traceId:iyque-1753492219774-9766
2025-07-26 09:10:23.879 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492223879-2641
2025-07-26 09:10:24.680 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753492224680-5985
2025-07-26 09:10:24.680 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753492224680-9188
2025-07-26 09:10:28.104 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492228104-5858
2025-07-26 09:10:30.533 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateSopState 业务请求traceId:iyque-1753492230533-1178
2025-07-26 09:10:30.584 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492230584-4082
2025-07-26 09:10:33.094 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753492233094-729
2025-07-26 09:10:33.094 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948913572485808129 业务请求traceId:iyque-1753492233094-979
2025-07-26 09:10:33.094 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753492233094-6722
2025-07-26 09:10:33.288 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753492233288-4498
2025-07-26 09:10:39.548 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753492239548-5838
2025-07-26 09:10:50.671 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateWeSop 业务请求traceId:iyque-1753492250671-1930
2025-07-26 09:10:51.724 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492251724-2249
2025-07-26 09:11:50.930 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailTab/1948913572485808129 业务请求traceId:iyque-1753492310930-6773
2025-07-26 09:11:50.930 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948913572485808129 业务请求traceId:iyque-1753492310930-8187
2025-07-26 09:11:50.930 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753492310930-5879
2025-07-26 09:12:06.882 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findCustomerExecuteContent 业务请求traceId:iyque-1753492326882-1763
2025-07-26 09:12:08.962 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findCustomerExecuteContent 业务请求traceId:iyque-1753492328962-6424
2025-07-26 09:12:36.412 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753492356412-3178
2025-07-26 09:17:30.696 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:17:30.697 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@scrm-system -> []
2025-07-26 09:17:37.752 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-26 09:17:37.752 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-26 09:17:37.753 [Thread-28] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-26 09:17:37.753 [Thread-28] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-26 09:17:37.760 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-26 09:17:37.773 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-26 09:17:37.774 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 09:17:37.774 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-gateWay:**************:6180 from beat map.
2025-07-26 09:17:37.774 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-26 09:17:37.813 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 09:17:37.813 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
