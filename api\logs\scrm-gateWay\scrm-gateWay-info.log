2025-07-26 09:01:48.644 [main] INFO  org.scrm.ScrmGatewayApplication - The following 1 profile is active: "dev"
2025-07-26 09:01:48.660 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:01:48.660 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:01:48.661 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:01:48.661 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:01:48.661 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:01:48.661 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:01:48.661 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:01:49.502 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:01:49.504 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:01:49.550 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-07-26 09:01:49.747 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[org.scrm]' package. Please check your configuration.
2025-07-26 09:01:49.821 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=24301223-7113-33b5-b484-079c86d537e1
2025-07-26 09:01:50.128 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.136 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.138 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.144 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.150 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.152 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.153 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$465/495885630] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.161 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.169 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.218 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.232 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.309 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:01:50.314 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.316 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:50.317 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:01:52.037 [main] INFO  o.s.g.s.GatewayDynamicRouteService - gateway route init...
2025-07-26 09:01:52.616 [main] INFO  o.s.g.s.GatewayDynamicRouteService - 获取网关当前配置:
[{
	"id": "scrm-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/auth/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"name": "ValidateCodeFilter"
	}, {
		"name": "CacheRequestFilter"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-auth-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/system/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-wechat-api",
	"order": 2,
	"predicates": [{
		"args": {
			"pattern": "/wecom/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"args": {
			"_genkey_0": "1"
		},
		"name": "StripPrefix"
	}],
	"uri": "lb://scrm-wechat-api"
}, {
	"id": "scrm-web-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/open/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-web-api"
}, {
	"id": "scrm-file",
	"order": 4,
	"predicates": [{
		"args": {
			"pattern": "/file/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-file"
}, {
	"id": "scrm-auth-common",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/common/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-mobile-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/wx-api/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-mobile-api"
}, {
	"id": "scrm-open-ai",
	"order": 6,
	"predicates": [{
		"args": {
			"pattern": "/ai/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-open-ai"
}]
2025-07-26 09:01:52.631 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-system', predicates=[PredicateDefinition{name='Path', args={pattern=/auth/**}}], filters=[FilterDefinition{name='ValidateCodeFilter', args={}}, FilterDefinition{name='CacheRequestFilter', args={}}], uri=lb://scrm-system, order=0, metadata={}}
2025-07-26 09:01:53.833 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-07-26 09:01:53.834 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-26 09:01:53.953 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:01:53.953 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:01:53.954 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:01:54.133 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-system', predicates=[PredicateDefinition{name='Path', args={pattern=/system/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-26 09:01:54.141 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-wechat-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wecom/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://scrm-wechat-api, order=2, metadata={}}
2025-07-26 09:01:54.150 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-web-api', predicates=[PredicateDefinition{name='Path', args={pattern=/open/**}}], filters=[], uri=lb://scrm-web-api, order=3, metadata={}}
2025-07-26 09:01:54.158 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-file', predicates=[PredicateDefinition{name='Path', args={pattern=/file/**}}], filters=[], uri=lb://scrm-file, order=4, metadata={}}
2025-07-26 09:01:54.165 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-common', predicates=[PredicateDefinition{name='Path', args={pattern=/common/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-26 09:01:54.174 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-mobile-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wx-api/**}}], filters=[], uri=lb://scrm-mobile-api, order=3, metadata={}}
2025-07-26 09:01:54.180 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-open-ai', predicates=[PredicateDefinition{name='Path', args={pattern=/ai/**}}], filters=[], uri=lb://scrm-open-ai, order=6, metadata={}}
2025-07-26 09:01:54.189 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] gateway-router+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:01:54.191 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=gateway-router, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:01:54.751 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -**********
2025-07-26 09:01:55.018 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-07-26 09:01:55.181 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:01:56.382 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-26 09:01:56.923 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:01:56.925 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:01:57.853 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:01:58.050 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 6180
2025-07-26 09:01:58.324 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6180, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-gateWay', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:01:58.325 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:01:58.361 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-gateWay **************:6180 register finished
2025-07-26 09:01:58.412 [main] INFO  org.scrm.ScrmGatewayApplication - Started ScrmGatewayApplication in 11.683 seconds (JVM running for 13.081)
2025-07-26 09:01:58.415 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:01:58.415 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:01:58.415 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:01:58.416 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:01:58.416 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:01:58.417 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:01:58.417 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:01:58.417 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:01:58.455 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:01:58.460 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:01:59.067 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:01:59.070 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:28.144 [boundedElastic-3] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:28.145 [boundedElastic-3] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:28.161 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:28.163 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:28.163 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:28.165 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:36.543 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753491816543-8376
2025-07-26 09:03:36.896 [reactor-http-nio-2] ERROR org.scrm.gateway.filter.AuthFilter - [鉴权异常处理]请求路径:/system/user/getInfo,msg:登录状态已过期
2025-07-26 09:03:46.907 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/auth/login 业务请求traceId:iyque-1753491826907-7051
2025-07-26 09:03:48.021 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753491828021-2928
2025-07-26 09:03:48.425 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753491828425-7549
2025-07-26 09:03:48.426 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753491828426-8767
2025-07-26 09:03:48.936 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/getWeIndex 业务请求traceId:iyque-1753491828936-5748
2025-07-26 09:03:49.534 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/trajectory/findCompanyDynamics 业务请求traceId:iyque-1753491829534-4730
2025-07-26 09:03:49.542 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/getRealCustomerCnt 业务请求traceId:iyque-1753491829542-4776
2025-07-26 09:03:49.546 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/operation/group/member/getGroupMemberRealNoPageCnt 业务请求traceId:iyque-1753491829546-3020
2025-07-26 09:03:49.990 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/trajectory/findCompanyDynamics 业务请求traceId:iyque-1753491829990-4354
2025-07-26 09:03:55.413 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753491835413-1415
2025-07-26 09:03:59.661 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateSopState 业务请求traceId:iyque-1753491839661-968
2025-07-26 09:03:59.748 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753491839748-5842
2025-07-26 09:04:07.989 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/1948650536413155330 业务请求traceId:iyque-1753491847989-3438
2025-07-26 09:04:08.209 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753491848209-8589
2025-07-26 09:04:33.659 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [polling-resp] config changed. dataId=scrm-common.yml, group=DEFAULT_GROUP, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:04:33.659 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - get changedGroupKeys:[scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505]
2025-07-26 09:04:33.735 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [data-received] dataId=scrm-common.yml, group=DEFAULT_GROUP, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, md5=b0abac52cbc78c9c8caa15d5d5109373, content=scrm:
  baiduMapsAk: HMM2G1pfhLrrtjI2dgOvQtokNqazExUY
  h5Domain: https://wework.doha1000day.com/mob..., type=yaml
2025-07-26 09:04:33.735 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-context] dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373
2025-07-26 09:04:33.992 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:04:33.992 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:04:33.992 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:04:33.992 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:04:33.992 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:04:33.992 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:04:34.107 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[scrm-gateWay] & group[DEFAULT_GROUP]
2025-07-26 09:04:34.184 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[scrm-gateWay-dev.yml] & group[DEFAULT_GROUP]
2025-07-26 09:04:34.184 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-scrm-gateWay-dev.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-gateWay.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-gateWay,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-common.yml,DEFAULT_GROUP'}]
2025-07-26 09:04:34.342 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.boot.SpringApplication - The following 1 profile is active: "dev"
2025-07-26 09:04:34.371 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.boot.SpringApplication - Started application in 0.629 seconds (JVM running for 169.04)
2025-07-26 09:04:34.505 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-error] dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@2dbf6f90 tx={}
org.springframework.boot.context.properties.bind.BindException: Failed to bind properties under 'spring.datasource.druid' to javax.sql.DataSource
	at org.springframework.boot.context.properties.bind.Binder.handleBindError(Binder.java:384)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:344)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:329)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:259)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:246)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBinder.bind(ConfigurationPropertiesBinder.java:95)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.bind(ConfigurationPropertiesBindingPostProcessor.java:89)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.postProcessBeforeInitialization(ConfigurationPropertiesBindingPostProcessor.java:78)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:431)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.rebind(ConfigurationPropertiesRebinder.java:105)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.rebind(ConfigurationPropertiesRebinder.java:83)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.onApplicationEvent(ConfigurationPropertiesRebinder.java:138)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.onApplicationEvent(ConfigurationPropertiesRebinder.java:51)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:421)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:378)
	at org.springframework.cloud.context.refresh.ContextRefresher.refreshEnvironment(ContextRefresher.java:103)
	at org.springframework.cloud.context.refresh.ContextRefresher.refresh(ContextRefresher.java:94)
	at org.springframework.cloud.endpoint.event.RefreshEventListener.handle(RefreshEventListener.java:72)
	at org.springframework.cloud.endpoint.event.RefreshEventListener.onApplicationEvent(RefreshEventListener.java:61)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:421)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:378)
	at com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1.innerReceive(NacosContextRefresher.java:133)
	at com.alibaba.nacos.api.config.listener.AbstractSharedListener.receiveConfigInfo(AbstractSharedListener.java:40)
	at com.alibaba.nacos.client.config.impl.CacheData$1.run(CacheData.java:210)
	at com.alibaba.nacos.client.config.impl.CacheData.safeNotifyListener(CacheData.java:241)
	at com.alibaba.nacos.client.config.impl.CacheData.checkListenerMd5(CacheData.java:180)
	at com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable.run(ClientWorker.java:569)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.IllegalStateException: Unable to set value for property url
	at org.springframework.boot.context.properties.bind.JavaBeanBinder$BeanProperty.setValue(JavaBeanBinder.java:367)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:101)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:83)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:59)
	at org.springframework.boot.context.properties.bind.Binder.lambda$bindDataObject$5(Binder.java:473)
	at org.springframework.boot.context.properties.bind.Binder$Context.withIncreasedDepth(Binder.java:587)
	at org.springframework.boot.context.properties.bind.Binder$Context.withDataObject(Binder.java:573)
	at org.springframework.boot.context.properties.bind.Binder$Context.access$300(Binder.java:534)
	at org.springframework.boot.context.properties.bind.Binder.bindDataObject(Binder.java:471)
	at org.springframework.boot.context.properties.bind.Binder.bindObject(Binder.java:411)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:340)
	... 41 common frames omitted
Caused by: java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder$BeanProperty.setValue(JavaBeanBinder.java:364)
	... 51 common frames omitted
Caused by: java.lang.UnsupportedOperationException: null
	at com.alibaba.druid.pool.DruidAbstractDataSource.setUrl(DruidAbstractDataSource.java:1203)
	... 56 common frames omitted
2025-07-26 09:04:34.506 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-listener] time cost=771ms in ClientWorker, dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@2dbf6f90 
2025-07-26 09:04:40.253 [Thread-31] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-26 09:04:40.253 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-26 09:04:40.259 [Thread-31] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-26 09:04:40.260 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-26 09:04:40.276 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-26 09:04:40.388 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-26 09:04:40.388 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 09:04:40.397 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-gateWay:**************:6180 from beat map.
2025-07-26 09:04:40.397 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-26 09:04:40.433 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 09:04:40.433 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
