<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.web.mapper.SysUserRoleMapper">

    <resultMap type="org.scrm.web.domain.SysUserRole" id="SysUserRoleResult">
        <result property="userId" column="user_id"/>
        <result property="roleId" column="role_id"/>
    </resultMap>

    <resultMap type="org.scrm.web.domain.vo.RoleUserVo" id="SysUserRoleVoResult">
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="weUserId" column="we_user_id"/>
        <result property="openUserid" column="open_userid"/>
    </resultMap>

    <select id="selectUserByRoleId" parameterType="Long" resultMap="SysUserRoleVoResult">
        select u.user_id,
               u.user_name,
               u.nick_name,
               u.we_user_id,
               u.open_userid
        from sys_user_role ur
                 left join sys_user u on u.user_id = ur.user_id
        where ur.role_id = #{roleId} and u.user_id is not NULL
    </select>

    <delete id="deleteUserRoleByUserId" parameterType="Long">
        delete
        from sys_user_role
        where user_id = #{userId}
    </delete>

    <select id="countUserRoleByRoleId" resultType="Integer">
        select count(1)
        from sys_user_role
        where role_id = #{roleId}
    </select>

    <delete id="deleteUserRole" parameterType="Long">
        delete from sys_user_role where user_id in
        <foreach collection="ids" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        <if test="roleId != null">
          and role_id = #{roleId}
        </if>
    </delete>

    <insert id="batchUserRole">
        insert into sys_user_role(user_id, role_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.userId},#{item.roleId})
        </foreach>
    </insert>

    <delete id="deleteUserRoleInfo" parameterType="org.scrm.web.domain.SysUserRole">
        delete
        from sys_user_role
        where user_id = #{userId}
          and role_id = #{roleId}
    </delete>

    <delete id="deleteUserRoleInfos">
        delete from sys_user_role where role_id=#{roleId} and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <delete id="deleteUserRoleByRoleId" parameterType="java.lang.Long">
        delete
        from sys_user_role
        where role_id = #{roleId}
    </delete>

    <select id="findAllUserRole" resultType="org.scrm.web.domain.SysUserRole">
        SELECT
            *
        FROM
            sys_user_role
    </select>

</mapper> 