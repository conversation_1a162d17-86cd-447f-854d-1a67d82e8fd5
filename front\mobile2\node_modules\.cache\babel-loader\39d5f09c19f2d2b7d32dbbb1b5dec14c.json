{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\thread-loader\\dist\\cjs.js!D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue", "mtime": 1753490486084}, {"path": "D:\\project\\scrm\\front\\mobile2\\babel.config.js", "mtime": 1750650120014}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751130691203}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751130701171}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751130703090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}