D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\annotation\AiMsgAop.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\domain\AiMessage.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\config\WebConfig.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\domain\WeAiMsgListQuery.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\controller\WeAiAssistantController.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\service\IWeAiSessionService.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\domain\PostBaseQuery.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\domain\WeAiMsgVo.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\listener\AiMessageListener.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\service\impl\WeAiSessionServiceImpl.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\interceptor\RequestContextInterceptor.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\ScrmOpenAiApplication.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\domain\WeAiMsgQuery.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\utils\WeAiSessionUtil.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\domain\WeAiCollectionMsgVo.java
D:\project\scrm\api\scrm-module\scrm-start\scrm-openai\src\main\java\org\scrm\domain\WeAiCollectionMsgQuery.java
