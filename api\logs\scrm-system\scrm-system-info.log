2025-07-26 09:02:25.871 [restartedMain] INFO  org.scrm.ScrmSystemApplication - The following 1 profile is active: "dev"
2025-07-26 09:02:25.901 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:02:25.901 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:02:25.902 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:02:25.902 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:02:25.902 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:02:25.902 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:02:25.902 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:02:28.082 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:02:28.086 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:02:28.317 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 208 ms. Found 0 Redis repository interfaces.
2025-07-26 09:02:29.071 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=bbaafe60-3136-36a4-aeea-fca110465031
2025-07-26 09:02:29.768 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$86399661] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.893 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.907 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.913 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.924 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.935 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.939 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.941 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$527/701413169] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.964 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.978 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:30.036 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:30.058 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:30.563 [restartedMain] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:02:31.701 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7880 (http)
2025-07-26 09:02:31.713 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7880"]
2025-07-26 09:02:31.713 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 09:02:31.713 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 09:02:31.784 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-26 09:02:31.784 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5882 ms
2025-07-26 09:02:34.498 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:02:36.004 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-26 09:02:48.780 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-26 09:02:48.912 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:49.908 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:49.926 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:50.645 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:51.180 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:51.522 [restartedMain] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 09:02:51.684 [redisson-netty-5-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:02:51.735 [redisson-netty-5-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:02:52.442 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:52.653 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:52.666 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:54.312 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:54.760 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:54.830 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:56.984 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:59.818 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:00.528 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:01.369 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:01.444 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:03.498 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:05.247 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:03:05.249 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:03:06.562 [restartedMain] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure any request
2025-07-26 09:03:07.529 [restartedMain] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:03:07.757 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:03:07.758 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:03:07.758 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:03:07.993 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7880"]
2025-07-26 09:03:08.059 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7880 (http) with context path ''
2025-07-26 09:03:08.062 [restartedMain] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=7880, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-system', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:03:08.063 [restartedMain] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-system with instance: Instance{instanceId='null', ip='**************', port=7880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:03:08.100 [restartedMain] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-system **************:7880 register finished
2025-07-26 09:03:08.266 [restartedMain] INFO  org.scrm.ScrmSystemApplication - Started ScrmSystemApplication in 44.65 seconds (JVM running for 46.103)
2025-07-26 09:03:08.273 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:08.275 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:08.276 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:08.276 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:08.276 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:08.277 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:08.278 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:08.278 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:09.058 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:09.064 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:47.224 [http-nio-7880-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 09:03:47.224 [http-nio-7880-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-26 09:03:47.227 [http-nio-7880-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-26 09:03:48.127 [http-nio-7880-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@37563444[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:04:33.653 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [polling-resp] config changed. dataId=scrm-common.yml, group=DEFAULT_GROUP, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:04:33.654 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - get changedGroupKeys:[scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505]
2025-07-26 09:04:33.727 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [data-received] dataId=scrm-common.yml, group=DEFAULT_GROUP, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, md5=b0abac52cbc78c9c8caa15d5d5109373, content=scrm:
  baiduMapsAk: HMM2G1pfhLrrtjI2dgOvQtokNqazExUY
  h5Domain: https://wework.doha1000day.com/mob..., type=yaml
2025-07-26 09:04:33.728 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-context] dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373
2025-07-26 09:04:33.989 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-26 09:04:33.998 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:04:33.999 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:04:33.999 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:04:33.999 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:04:33.999 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:04:33.999 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:04:34.101 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[scrm-system] & group[DEFAULT_GROUP]
2025-07-26 09:04:34.167 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[scrm-system-dev.yml] & group[DEFAULT_GROUP]
2025-07-26 09:04:34.168 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-scrm-system-dev.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-system.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-system,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-common.yml,DEFAULT_GROUP'}]
2025-07-26 09:04:34.315 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.boot.SpringApplication - The following 1 profile is active: "dev"
2025-07-26 09:04:34.344 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.boot.SpringApplication - Started application in 0.61 seconds (JVM running for 132.181)
2025-07-26 09:04:34.485 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-error] dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@2e835218 tx={}
org.springframework.boot.context.properties.bind.BindException: Failed to bind properties under 'spring.datasource.druid' to javax.sql.DataSource
	at org.springframework.boot.context.properties.bind.Binder.handleBindError(Binder.java:384)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:344)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:329)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:259)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:246)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBinder.bind(ConfigurationPropertiesBinder.java:95)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.bind(ConfigurationPropertiesBindingPostProcessor.java:89)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.postProcessBeforeInitialization(ConfigurationPropertiesBindingPostProcessor.java:78)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:431)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.rebind(ConfigurationPropertiesRebinder.java:105)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.rebind(ConfigurationPropertiesRebinder.java:83)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.onApplicationEvent(ConfigurationPropertiesRebinder.java:138)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.onApplicationEvent(ConfigurationPropertiesRebinder.java:51)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:421)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:378)
	at org.springframework.cloud.context.refresh.ContextRefresher.refreshEnvironment(ContextRefresher.java:103)
	at org.springframework.cloud.context.refresh.ContextRefresher.refresh(ContextRefresher.java:94)
	at org.springframework.cloud.endpoint.event.RefreshEventListener.handle(RefreshEventListener.java:72)
	at org.springframework.cloud.endpoint.event.RefreshEventListener.onApplicationEvent(RefreshEventListener.java:61)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:421)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:378)
	at com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1.innerReceive(NacosContextRefresher.java:133)
	at com.alibaba.nacos.api.config.listener.AbstractSharedListener.receiveConfigInfo(AbstractSharedListener.java:40)
	at com.alibaba.nacos.client.config.impl.CacheData$1.run(CacheData.java:210)
	at com.alibaba.nacos.client.config.impl.CacheData.safeNotifyListener(CacheData.java:241)
	at com.alibaba.nacos.client.config.impl.CacheData.checkListenerMd5(CacheData.java:180)
	at com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable.run(ClientWorker.java:569)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.IllegalStateException: Unable to set value for property url
	at org.springframework.boot.context.properties.bind.JavaBeanBinder$BeanProperty.setValue(JavaBeanBinder.java:367)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:101)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:83)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:59)
	at org.springframework.boot.context.properties.bind.Binder.lambda$bindDataObject$5(Binder.java:473)
	at org.springframework.boot.context.properties.bind.Binder$Context.withIncreasedDepth(Binder.java:587)
	at org.springframework.boot.context.properties.bind.Binder$Context.withDataObject(Binder.java:573)
	at org.springframework.boot.context.properties.bind.Binder$Context.access$300(Binder.java:534)
	at org.springframework.boot.context.properties.bind.Binder.bindDataObject(Binder.java:471)
	at org.springframework.boot.context.properties.bind.Binder.bindObject(Binder.java:411)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:340)
	... 41 common frames omitted
Caused by: java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder$BeanProperty.setValue(JavaBeanBinder.java:364)
	... 51 common frames omitted
Caused by: java.lang.UnsupportedOperationException: null
	at com.alibaba.druid.pool.DruidAbstractDataSource.setUrl(DruidAbstractDataSource.java:1203)
	... 56 common frames omitted
2025-07-26 09:04:34.488 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-listener] time cost=760ms in ClientWorker, dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@2e835218 
2025-07-26 09:04:40.283 [Thread-107] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-26 09:04:40.283 [Thread-9] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-26 09:04:40.285 [Thread-107] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-26 09:04:40.285 [Thread-9] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-26 09:04:40.417 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 09:04:40.418 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-system:**************:7880 from beat map.
2025-07-26 09:04:40.418 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-system with instance: Instance{instanceId='null', ip='**************', port=7880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
