2025-07-26 09:02:25.871 [restartedMain] INFO  org.scrm.ScrmSystemApplication - The following 1 profile is active: "dev"
2025-07-26 09:02:25.901 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:02:25.901 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:02:25.902 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:02:25.902 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:02:25.902 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:02:25.902 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:02:25.902 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:02:28.082 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:02:28.086 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:02:28.317 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 208 ms. Found 0 Redis repository interfaces.
2025-07-26 09:02:29.071 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=bbaafe60-3136-36a4-aeea-fca110465031
2025-07-26 09:02:29.768 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$86399661] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.893 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.907 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.913 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.924 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.935 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.939 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.941 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$527/701413169] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.964 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:29.978 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:30.036 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:30.058 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:02:30.563 [restartedMain] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:02:31.701 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7880 (http)
2025-07-26 09:02:31.713 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7880"]
2025-07-26 09:02:31.713 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 09:02:31.713 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 09:02:31.784 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-26 09:02:31.784 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5882 ms
2025-07-26 09:02:34.498 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:02:36.004 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-26 09:02:48.780 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-26 09:02:48.912 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:49.908 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:49.926 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:50.645 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:51.180 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:51.522 [restartedMain] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 09:02:51.684 [redisson-netty-5-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:02:51.735 [redisson-netty-5-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:02:52.442 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:52.653 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:52.666 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:54.312 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:54.760 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:54.830 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:56.984 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:02:59.818 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:00.528 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:01.369 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:01.444 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:03.498 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:03:05.247 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:03:05.249 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:03:06.562 [restartedMain] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure any request
2025-07-26 09:03:07.529 [restartedMain] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:03:07.757 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:03:07.758 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:03:07.758 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:03:07.993 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7880"]
2025-07-26 09:03:08.059 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7880 (http) with context path ''
2025-07-26 09:03:08.062 [restartedMain] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=7880, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-system', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:03:08.063 [restartedMain] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-system with instance: Instance{instanceId='null', ip='**************', port=7880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:03:08.100 [restartedMain] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-system **************:7880 register finished
2025-07-26 09:03:08.266 [restartedMain] INFO  org.scrm.ScrmSystemApplication - Started ScrmSystemApplication in 44.65 seconds (JVM running for 46.103)
2025-07-26 09:03:08.273 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:08.275 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:08.276 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:08.276 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:08.276 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:08.277 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:08.278 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:03:08.278 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:03:09.058 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:09.064 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:03:47.224 [http-nio-7880-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 09:03:47.224 [http-nio-7880-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-26 09:03:47.227 [http-nio-7880-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-26 09:03:48.127 [http-nio-7880-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@37563444[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:04:33.653 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [polling-resp] config changed. dataId=scrm-common.yml, group=DEFAULT_GROUP, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:04:33.654 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - get changedGroupKeys:[scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505]
2025-07-26 09:04:33.727 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [data-received] dataId=scrm-common.yml, group=DEFAULT_GROUP, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, md5=b0abac52cbc78c9c8caa15d5d5109373, content=scrm:
  baiduMapsAk: HMM2G1pfhLrrtjI2dgOvQtokNqazExUY
  h5Domain: https://wework.doha1000day.com/mob..., type=yaml
2025-07-26 09:04:33.728 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-context] dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373
2025-07-26 09:04:33.989 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-26 09:04:33.998 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:04:33.999 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:04:33.999 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:04:33.999 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:04:33.999 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:04:33.999 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:04:34.101 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[scrm-system] & group[DEFAULT_GROUP]
2025-07-26 09:04:34.167 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] WARN  c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[scrm-system-dev.yml] & group[DEFAULT_GROUP]
2025-07-26 09:04:34.168 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-scrm-system-dev.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-system.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-system,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-scrm-common.yml,DEFAULT_GROUP'}]
2025-07-26 09:04:34.315 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.boot.SpringApplication - The following 1 profile is active: "dev"
2025-07-26 09:04:34.344 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  o.s.boot.SpringApplication - Started application in 0.61 seconds (JVM running for 132.181)
2025-07-26 09:04:34.485 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-error] dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@2e835218 tx={}
org.springframework.boot.context.properties.bind.BindException: Failed to bind properties under 'spring.datasource.druid' to javax.sql.DataSource
	at org.springframework.boot.context.properties.bind.Binder.handleBindError(Binder.java:384)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:344)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:329)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:259)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:246)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBinder.bind(ConfigurationPropertiesBinder.java:95)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.bind(ConfigurationPropertiesBindingPostProcessor.java:89)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.postProcessBeforeInitialization(ConfigurationPropertiesBindingPostProcessor.java:78)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:431)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.rebind(ConfigurationPropertiesRebinder.java:105)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.rebind(ConfigurationPropertiesRebinder.java:83)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.onApplicationEvent(ConfigurationPropertiesRebinder.java:138)
	at org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder.onApplicationEvent(ConfigurationPropertiesRebinder.java:51)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:421)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:378)
	at org.springframework.cloud.context.refresh.ContextRefresher.refreshEnvironment(ContextRefresher.java:103)
	at org.springframework.cloud.context.refresh.ContextRefresher.refresh(ContextRefresher.java:94)
	at org.springframework.cloud.endpoint.event.RefreshEventListener.handle(RefreshEventListener.java:72)
	at org.springframework.cloud.endpoint.event.RefreshEventListener.onApplicationEvent(RefreshEventListener.java:61)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:421)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:378)
	at com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1.innerReceive(NacosContextRefresher.java:133)
	at com.alibaba.nacos.api.config.listener.AbstractSharedListener.receiveConfigInfo(AbstractSharedListener.java:40)
	at com.alibaba.nacos.client.config.impl.CacheData$1.run(CacheData.java:210)
	at com.alibaba.nacos.client.config.impl.CacheData.safeNotifyListener(CacheData.java:241)
	at com.alibaba.nacos.client.config.impl.CacheData.checkListenerMd5(CacheData.java:180)
	at com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable.run(ClientWorker.java:569)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.IllegalStateException: Unable to set value for property url
	at org.springframework.boot.context.properties.bind.JavaBeanBinder$BeanProperty.setValue(JavaBeanBinder.java:367)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:101)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:83)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder.bind(JavaBeanBinder.java:59)
	at org.springframework.boot.context.properties.bind.Binder.lambda$bindDataObject$5(Binder.java:473)
	at org.springframework.boot.context.properties.bind.Binder$Context.withIncreasedDepth(Binder.java:587)
	at org.springframework.boot.context.properties.bind.Binder$Context.withDataObject(Binder.java:573)
	at org.springframework.boot.context.properties.bind.Binder$Context.access$300(Binder.java:534)
	at org.springframework.boot.context.properties.bind.Binder.bindDataObject(Binder.java:471)
	at org.springframework.boot.context.properties.bind.Binder.bindObject(Binder.java:411)
	at org.springframework.boot.context.properties.bind.Binder.bind(Binder.java:340)
	... 41 common frames omitted
Caused by: java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.context.properties.bind.JavaBeanBinder$BeanProperty.setValue(JavaBeanBinder.java:364)
	... 51 common frames omitted
Caused by: java.lang.UnsupportedOperationException: null
	at com.alibaba.druid.pool.DruidAbstractDataSource.setUrl(DruidAbstractDataSource.java:1203)
	... 56 common frames omitted
2025-07-26 09:04:34.488 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [notify-listener] time cost=760ms in ClientWorker, dataId=scrm-common.yml, group=DEFAULT_GROUP, md5=b0abac52cbc78c9c8caa15d5d5109373, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@2e835218 
2025-07-26 09:04:40.283 [Thread-107] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-26 09:04:40.283 [Thread-9] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-26 09:04:40.285 [Thread-107] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-26 09:04:40.285 [Thread-9] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-26 09:04:40.417 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 09:04:40.418 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-system:**************:7880 from beat map.
2025-07-26 09:04:40.418 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-system with instance: Instance{instanceId='null', ip='**************', port=7880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-26 09:05:06.849 [restartedMain] INFO  org.scrm.ScrmSystemApplication - The following 1 profile is active: "dev"
2025-07-26 09:05:06.883 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:05:06.883 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:05:06.883 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:05:06.883 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:05:06.883 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:05:06.883 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:05:06.883 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:05:09.504 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:05:09.508 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:05:09.770 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 240 ms. Found 0 Redis repository interfaces.
2025-07-26 09:05:10.436 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=bbaafe60-3136-36a4-aeea-fca110465031
2025-07-26 09:05:11.032 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$9626687e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:11.126 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:11.136 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:11.140 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:11.148 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:11.159 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:11.163 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:11.164 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$527/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:11.178 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:11.191 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:11.261 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:11.286 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:05:11.780 [restartedMain] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:05:12.774 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7880 (http)
2025-07-26 09:05:12.785 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7880"]
2025-07-26 09:05:12.786 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 09:05:12.787 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 09:05:12.864 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-26 09:05:12.864 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5981 ms
2025-07-26 09:05:15.247 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:05:16.669 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-26 09:05:29.435 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-26 09:05:29.540 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:30.591 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:30.609 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:31.247 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:31.618 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:31.876 [restartedMain] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 09:05:31.991 [redisson-netty-5-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:05:32.000 [redisson-netty-5-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:05:32.639 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:32.880 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:32.890 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:34.549 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:35.114 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:35.193 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:37.660 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:41.072 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:42.411 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:44.117 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:44.330 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:46.735 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:05:48.627 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:05:48.628 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:05:49.483 [restartedMain] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure any request
2025-07-26 09:05:50.431 [restartedMain] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:05:50.705 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:05:50.707 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:05:50.707 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:05:50.946 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7880"]
2025-07-26 09:05:50.960 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7880 (http) with context path ''
2025-07-26 09:05:50.964 [restartedMain] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=7880, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-system', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:05:50.965 [restartedMain] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-system with instance: Instance{instanceId='null', ip='**************', port=7880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:05:51.002 [restartedMain] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-system **************:7880 register finished
2025-07-26 09:05:51.167 [restartedMain] INFO  org.scrm.ScrmSystemApplication - Started ScrmSystemApplication in 46.975 seconds (JVM running for 48.405)
2025-07-26 09:05:51.175 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:05:51.178 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:05:51.179 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:05:51.179 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:05:51.180 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:05:51.180 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:05:51.182 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:05:51.182 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:05:52.018 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:05:52.023 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:06:15.747 [http-nio-7880-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 09:06:15.748 [http-nio-7880-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-26 09:06:15.751 [http-nio-7880-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-26 09:06:15.831 [http-nio-7880-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@2913e61[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:06:18.514 [http-nio-7880-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@64d1bbda[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:06:30.606 [http-nio-7880-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@534b640c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:06:46.970 [http-nio-7880-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@39c08fc5[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:07:09.179 [http-nio-7880-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@559240e8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:07:12.174 [http-nio-7880-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3054833d[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:09:22.909 [http-nio-7880-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@2dae8761[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:09:26.810 [http-nio-7880-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4f4a13e9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:17:21.954 [Thread-60] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 09:17:21.954 [Thread-60] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-system:**************:7880 from beat map.
2025-07-26 09:17:21.954 [Thread-60] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-system with instance: Instance{instanceId='null', ip='**************', port=7880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-26 09:17:21.986 [Thread-60] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 09:17:21.986 [Thread-60] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-07-26 09:17:22.056 [Thread-60] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-07-26 09:17:22.056 [Thread-60] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-07-26 09:17:25.097 [Thread-60] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-07-26 09:17:28.120 [Thread-60] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-07-26 09:17:28.120 [Thread-60] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-07-26 09:17:28.120 [Thread-60] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-07-26 09:17:28.120 [Thread-60] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-07-26 09:17:28.121 [Thread-60] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-07-26 09:17:28.121 [Thread-60] WARN  com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-07-26 09:17:28.121 [Thread-60] WARN  com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end
2025-07-26 09:17:28.121 [Thread-60] INFO  c.a.n.c.identify.CredentialWatcher - [null] CredentialWatcher is stopped
2025-07-26 09:17:28.121 [Thread-60] INFO  c.a.n.c.identify.CredentialService - [null] CredentialService is freed
2025-07-26 09:17:28.122 [Thread-60] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-07-26 09:17:28.122 [Thread-60] WARN  o.s.b.f.s.DisposableBeanAdapter - Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-07-26 09:17:28.205 [Thread-60] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-26 09:17:28.216 [Thread-60] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-26 09:17:30.154 [restartedMain] INFO  org.scrm.ScrmSystemApplication - The following 1 profile is active: "dev"
2025-07-26 09:17:30.165 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:17:30.165 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:17:30.165 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:17:30.165 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:17:30.165 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:17:30.165 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:17:30.165 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:17:34.508 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:17:34.508 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:17:34.832 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 323 ms. Found 0 Redis repository interfaces.
2025-07-26 09:17:35.212 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=d5c5cd23-6331-3845-af77-dbb7fe5e57ae
2025-07-26 09:17:35.374 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$9626687e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:17:35.408 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:17:35.414 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:17:35.417 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:17:35.421 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:17:35.429 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:17:35.433 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:17:35.434 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$527/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:17:35.460 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:17:35.472 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:17:35.482 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:17:35.567 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:17:36.639 [restartedMain] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:17:37.820 [Thread-113] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-26 09:17:37.822 [Thread-8] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-26 09:17:37.822 [Thread-113] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-26 09:17:37.823 [Thread-8] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-26 09:17:37.939 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7880 (http)
2025-07-26 09:17:37.959 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7794 ms
2025-07-26 09:39:39.867 [restartedMain] INFO  org.scrm.ScrmSystemApplication - The following 1 profile is active: "dev"
2025-07-26 09:39:39.913 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:39:39.913 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:39:39.913 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:39:39.914 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:39:39.914 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:39:39.914 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:39:39.914 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:39:43.874 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:39:43.886 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:39:44.313 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 388 ms. Found 0 Redis repository interfaces.
2025-07-26 09:39:45.192 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=bbaafe60-3136-36a4-aeea-fca110465031
2025-07-26 09:39:45.877 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$e9561e62] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:39:45.976 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:39:45.985 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:39:45.988 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:39:45.997 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:39:46.006 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:39:46.009 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:39:46.011 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$527/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:39:46.028 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:39:46.040 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:39:46.097 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:39:46.122 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:39:46.595 [restartedMain] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:39:47.648 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7880 (http)
2025-07-26 09:39:47.667 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7880"]
2025-07-26 09:39:47.667 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 09:39:47.667 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 09:39:47.768 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-26 09:39:47.768 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7854 ms
2025-07-26 09:39:50.495 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:39:52.095 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-26 09:40:10.551 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-26 09:40:10.712 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:11.859 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:11.883 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:12.834 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:13.500 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:13.940 [restartedMain] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 09:40:14.105 [redisson-netty-5-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:40:14.115 [redisson-netty-5-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:40:15.011 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:15.336 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:15.353 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:17.986 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:18.642 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:18.725 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:21.653 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:24.894 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:25.610 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:26.474 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:26.548 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:28.287 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:40:29.923 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:40:29.924 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:40:31.082 [restartedMain] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure any request
2025-07-26 09:40:32.118 [restartedMain] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:40:32.448 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:40:32.449 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:40:32.449 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:40:32.681 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7880"]
2025-07-26 09:40:32.750 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7880 (http) with context path ''
2025-07-26 09:40:32.755 [restartedMain] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=7880, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-system', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:40:32.756 [restartedMain] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-system with instance: Instance{instanceId='null', ip='**************', port=7880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:40:32.800 [restartedMain] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-system **************:7880 register finished
2025-07-26 09:40:33.028 [restartedMain] INFO  org.scrm.ScrmSystemApplication - Started ScrmSystemApplication in 55.469 seconds (JVM running for 56.829)
2025-07-26 09:40:33.035 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:40:33.037 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:40:33.038 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:40:33.038 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:40:33.039 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:40:33.039 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:40:33.040 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-26 09:40:33.040 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system.yml, group=DEFAULT_GROUP, cnt=1
2025-07-26 09:40:33.760 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:40:33.765 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:41:08.852 [http-nio-7880-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 09:41:08.852 [http-nio-7880-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-26 09:41:08.858 [http-nio-7880-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-07-26 09:41:08.978 [http-nio-7880-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5717e5e4[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:41:21.639 [http-nio-7880-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@581f95c1[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:41:24.807 [http-nio-7880-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@363f259c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:41:46.708 [http-nio-7880-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@42d936c2[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:41:51.596 [http-nio-7880-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@661de868[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:45:09.100 [http-nio-7880-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6a6ae78a[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:45:13.185 [http-nio-7880-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=ba357e79-b0dc-40bf-91f4-0dba8c6c37a1, corpId=ww44eff3837ea05d08, corpName=图伽（北京）健康科技有限公司, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753491827740, expireTime=1753535027740, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@65d3e8a9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/07/10/9bca7457-8b03-46ec-bc56-84bf77f1e41e.png
  password=$2a$10$PC7sSiA6I46WuQfKyC3gNOY9ue6XJXVRAgSe5c5vBb1SdYTYfn3xy
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-26 09:51:31.765 [Thread-69] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 09:51:31.765 [Thread-69] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-system:**************:7880 from beat map.
2025-07-26 09:51:31.765 [Thread-69] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-system with instance: Instance{instanceId='null', ip='**************', port=7880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-26 09:51:31.800 [Thread-69] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 09:51:31.800 [Thread-69] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-07-26 09:51:33.850 [Thread-69] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-07-26 09:51:33.850 [Thread-69] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-07-26 09:51:37.018 [Thread-69] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-07-26 09:51:40.202 [Thread-69] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-07-26 09:51:40.203 [Thread-69] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-07-26 09:51:40.205 [Thread-69] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-07-26 09:51:40.205 [Thread-69] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-07-26 09:51:40.206 [Thread-69] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-07-26 09:51:40.207 [Thread-69] WARN  com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-07-26 09:51:40.207 [Thread-69] WARN  com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end
2025-07-26 09:51:40.208 [Thread-69] INFO  c.a.n.c.identify.CredentialWatcher - [null] CredentialWatcher is stopped
2025-07-26 09:51:40.208 [Thread-69] INFO  c.a.n.c.identify.CredentialService - [null] CredentialService is freed
2025-07-26 09:51:40.209 [Thread-69] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-07-26 09:51:40.213 [Thread-69] WARN  o.s.b.f.s.DisposableBeanAdapter - Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-07-26 09:51:40.798 [Thread-69] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-26 09:51:40.831 [Thread-69] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-26 09:51:43.820 [restartedMain] INFO  org.scrm.ScrmSystemApplication - The following 1 profile is active: "dev"
2025-07-26 09:51:43.827 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:51:43.827 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:51:43.827 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:51:43.856 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:51:43.857 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:51:43.857 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:51:43.857 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:51:45.920 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:51:45.920 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:51:46.101 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 180 ms. Found 0 Redis repository interfaces.
2025-07-26 09:51:46.589 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=d5c5cd23-6331-3845-af77-dbb7fe5e57ae
2025-07-26 09:51:46.743 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$e9561e62] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:51:46.775 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:51:46.781 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:51:46.785 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:51:46.788 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:51:46.795 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:51:46.797 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:51:46.798 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$527/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:51:46.809 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:51:46.817 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:51:46.825 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:51:46.838 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:51:47.234 [restartedMain] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:51:47.600 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7880 (http)
2025-07-26 09:51:47.600 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 09:51:47.600 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 09:51:47.612 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-26 09:51:47.613 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3756 ms
2025-07-26 09:51:48.353 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:51:50.076 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
2025-07-26 09:52:16.071 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-26 09:52:16.132 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:52:16.766 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:52:16.782 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:52:17.923 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:52:18.601 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:52:19.042 [restartedMain] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 09:52:19.079 [redisson-netty-11-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:52:19.093 [redisson-netty-11-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:52:20.002 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:52:20.330 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:52:20.343 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:52:22.000 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sopReevaluationEventListener': Unsatisfied dependency expressed through field 'iWeSopBaseService'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.scrm.service.IWeSopBaseService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2025-07-26 09:52:22.018 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-07-26 09:52:22.020 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-07-26 09:52:22.128 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-26 09:52:22.139 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-26 09:52:22.271 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field iWeSopBaseService in org.scrm.listener.SopReevaluationEventListener required a bean of type 'org.scrm.service.IWeSopBaseService' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'org.scrm.service.IWeSopBaseService' in your configuration.

2025-07-26 09:52:28.064 [restartedMain] INFO  org.scrm.ScrmSystemApplication - The following 1 profile is active: "dev"
2025-07-26 09:52:28.074 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:52:28.074 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:52:28.074 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:52:28.074 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:52:28.074 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:52:28.074 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:52:28.074 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:52:29.390 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:52:29.390 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:52:29.590 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 199 ms. Found 0 Redis repository interfaces.
2025-07-26 09:52:29.887 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=bbaafe60-3136-36a4-aeea-fca110465031
2025-07-26 09:52:29.989 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$e9561e62] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:52:30.016 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:52:30.022 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:52:30.025 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:52:30.028 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:52:30.033 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:52:30.035 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:52:30.035 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$527/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:52:30.048 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:52:30.055 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:52:30.062 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:52:30.077 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:52:30.486 [restartedMain] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:52:30.818 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7880 (http)
2025-07-26 09:52:30.819 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 09:52:30.819 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 09:52:30.830 [restartedMain] INFO  o.a.c.c.C.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-26 09:52:30.831 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2757 ms
2025-07-26 09:52:31.396 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:52:32.688 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} inited
2025-07-26 09:52:57.441 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-26 09:52:57.520 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:52:58.013 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:52:58.021 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:52:58.474 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:52:58.960 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:52:59.292 [restartedMain] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 09:52:59.334 [redisson-netty-17-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:52:59.346 [redisson-netty-17-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:53:00.129 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:53:00.363 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:53:00.374 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:53:02.373 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:53:02.788 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:53:02.882 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:53:07.381 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:53:12.057 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:53:12.601 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:53:13.344 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:53:13.408 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:53:15.436 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:53:18.289 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:53:18.290 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:53:19.103 [restartedMain] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure any request
2025-07-26 09:53:20.627 [restartedMain] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:53:21.479 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:53:21.481 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:53:21.481 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:53:21.589 [restartedMain] INFO  c.a.n.c.identify.CredentialWatcher - null No credential found
2025-07-26 09:53:21.681 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7880 (http) with context path ''
2025-07-26 09:53:21.820 [restartedMain] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=7880, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-system', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:53:21.821 [restartedMain] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-system with instance: Instance{instanceId='null', ip='**************', port=7880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:53:21.865 [restartedMain] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-system **************:7880 register finished
2025-07-26 09:53:22.230 [restartedMain] INFO  org.scrm.ScrmSystemApplication - Started ScrmSystemApplication in 54.95 seconds (JVM running for 826.031)
2025-07-26 09:53:22.234 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system-dev.yml, group=DEFAULT_GROUP, cnt=2
2025-07-26 09:53:22.234 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=2
2025-07-26 09:53:22.234 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system, group=DEFAULT_GROUP, cnt=2
2025-07-26 09:53:22.234 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system.yml, group=DEFAULT_GROUP, cnt=2
2025-07-26 09:53:22.237 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-26 09:53:22.774 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:53:22.775 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:57:01.972 [Thread-144] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 09:57:01.972 [Thread-144] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-system:**************:7880 from beat map.
2025-07-26 09:57:01.972 [Thread-144] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-system with instance: Instance{instanceId='null', ip='**************', port=7880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-26 09:57:02.006 [Thread-144] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 09:57:02.006 [Thread-144] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-07-26 09:57:03.804 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:57:03.805 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> []
2025-07-26 09:57:03.816 [Thread-144] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-07-26 09:57:03.816 [Thread-144] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-07-26 09:57:05.957 [Thread-144] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-07-26 09:57:09.021 [Thread-144] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-07-26 09:57:09.021 [Thread-144] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-07-26 09:57:09.052 [Thread-144] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-07-26 09:57:09.052 [Thread-144] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-07-26 09:57:09.052 [Thread-144] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-07-26 09:57:09.053 [Thread-144] WARN  com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-07-26 09:57:09.053 [Thread-144] WARN  com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end
2025-07-26 09:57:09.053 [Thread-144] INFO  c.a.n.c.identify.CredentialWatcher - [null] CredentialWatcher is stopped
2025-07-26 09:57:09.053 [Thread-144] INFO  c.a.n.c.identify.CredentialService - [null] CredentialService is freed
2025-07-26 09:57:09.053 [Thread-144] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-07-26 09:57:09.053 [Thread-144] WARN  o.s.b.f.s.DisposableBeanAdapter - Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-07-26 09:57:09.148 [Thread-144] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closing ...
2025-07-26 09:57:09.154 [Thread-144] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closed
2025-07-26 09:57:13.634 [restartedMain] INFO  org.scrm.ScrmSystemApplication - The following 1 profile is active: "dev"
2025-07-26 09:57:13.647 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:57:13.647 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:57:13.647 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:57:13.647 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:57:13.647 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:57:13.647 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:57:13.647 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:57:16.396 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:57:16.396 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:57:16.599 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 202 ms. Found 0 Redis repository interfaces.
2025-07-26 09:57:16.962 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=d772f8df-caa7-33c7-8804-9db626d97f69
2025-07-26 09:57:17.082 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$e9561e62] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:57:17.114 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:57:17.118 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:57:17.121 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:57:17.124 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:57:17.131 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:57:17.135 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:57:17.137 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$527/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:57:17.156 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:57:17.167 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:57:17.180 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:57:17.195 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:57:17.783 [restartedMain] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:57:18.202 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7880 (http)
2025-07-26 09:57:18.203 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 09:57:18.203 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 09:57:18.214 [restartedMain] INFO  o.a.c.c.C.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-26 09:57:18.215 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4568 ms
2025-07-26 09:57:19.045 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:57:20.228 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} inited
2025-07-26 09:57:32.003 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-26 09:57:32.045 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:57:32.409 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:57:32.417 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:57:32.882 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:57:33.251 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:57:33.534 [restartedMain] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 09:57:33.555 [redisson-netty-23-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:57:33.564 [redisson-netty-23-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:57:34.028 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:57:34.214 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:57:34.221 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:57:35.187 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sopReevaluationEventListener': Unsatisfied dependency expressed through field 'iWeSopBaseService'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.scrm.service.IWeSopBaseService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2025-07-26 09:57:35.198 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} closing ...
2025-07-26 09:57:35.199 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} closed
2025-07-26 09:57:35.309 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-26 09:57:35.318 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-26 09:57:35.332 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field iWeSopBaseService in org.scrm.listener.SopReevaluationEventListener required a bean of type 'org.scrm.service.IWeSopBaseService' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'org.scrm.service.IWeSopBaseService' in your configuration.

2025-07-26 09:58:06.076 [restartedMain] INFO  org.scrm.ScrmSystemApplication - The following 1 profile is active: "dev"
2025-07-26 09:58:06.088 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:58:06.088 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 09:58:06.089 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 09:58:06.089 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 09:58:06.089 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 09:58:06.089 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 09:58:06.089 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 09:58:10.674 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 09:58:10.674 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 09:58:10.978 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 305 ms. Found 0 Redis repository interfaces.
2025-07-26 09:58:11.410 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=bbaafe60-3136-36a4-aeea-fca110465031
2025-07-26 09:58:11.611 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$e9561e62] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:58:11.648 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:58:11.655 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:58:11.660 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:58:11.663 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:58:11.673 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:58:11.678 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:58:11.679 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$527/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:58:11.702 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:58:11.715 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:58:11.728 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:58:11.749 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 09:58:12.413 [restartedMain] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 09:58:12.864 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7880 (http)
2025-07-26 09:58:12.865 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 09:58:12.865 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 09:58:12.876 [restartedMain] INFO  o.a.c.c.C.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-26 09:58:12.876 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6787 ms
2025-07-26 09:58:13.447 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 09:58:14.652 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-5} inited
2025-07-26 09:58:33.916 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-26 09:58:34.010 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:34.514 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:34.530 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:35.013 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:35.354 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:35.611 [restartedMain] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 09:58:35.629 [redisson-netty-29-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:58:35.635 [redisson-netty-29-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 09:58:36.378 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:36.757 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:36.771 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:38.545 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:39.134 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:39.214 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:40.848 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:43.084 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:43.567 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:44.145 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:44.195 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:45.615 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 09:58:46.530 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 09:58:46.530 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 09:58:46.874 [restartedMain] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure any request
2025-07-26 09:58:47.222 [restartedMain] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 09:58:47.368 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:58:47.368 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 09:58:47.368 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 09:58:47.473 [restartedMain] INFO  c.a.n.c.identify.CredentialWatcher - null No credential found
2025-07-26 09:58:47.514 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7880 (http) with context path ''
2025-07-26 09:58:47.516 [restartedMain] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=7880, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-system', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 09:58:47.516 [restartedMain] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-system with instance: Instance{instanceId='null', ip='**************', port=7880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 09:58:47.550 [restartedMain] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-system **************:7880 register finished
2025-07-26 09:58:47.729 [restartedMain] INFO  org.scrm.ScrmSystemApplication - Started ScrmSystemApplication in 43.255 seconds (JVM running for 1151.53)
2025-07-26 09:58:47.730 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system-dev.yml, group=DEFAULT_GROUP, cnt=3
2025-07-26 09:58:47.731 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=3
2025-07-26 09:58:47.731 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system, group=DEFAULT_GROUP, cnt=3
2025-07-26 09:58:47.731 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system.yml, group=DEFAULT_GROUP, cnt=3
2025-07-26 09:58:47.731 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-26 09:58:48.547 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 09:58:48.548 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 10:00:33.633 [Thread-170] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 10:00:33.634 [Thread-170] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-system:**************:7880 from beat map.
2025-07-26 10:00:33.634 [Thread-170] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-system with instance: Instance{instanceId='null', ip='**************', port=7880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-26 10:00:33.676 [Thread-170] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 10:00:33.676 [Thread-170] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-07-26 10:00:33.887 [Thread-170] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-07-26 10:00:33.887 [Thread-170] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-07-26 10:00:36.919 [Thread-170] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-07-26 10:00:39.981 [Thread-170] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-07-26 10:00:39.983 [Thread-170] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-07-26 10:00:39.983 [Thread-170] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-07-26 10:00:39.983 [Thread-170] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-07-26 10:00:39.983 [Thread-170] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-07-26 10:00:39.984 [Thread-170] WARN  com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-07-26 10:00:39.984 [Thread-170] WARN  com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end
2025-07-26 10:00:39.984 [Thread-170] INFO  c.a.n.c.identify.CredentialWatcher - [null] CredentialWatcher is stopped
2025-07-26 10:00:39.984 [Thread-170] INFO  c.a.n.c.identify.CredentialService - [null] CredentialService is freed
2025-07-26 10:00:39.984 [Thread-170] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-07-26 10:00:39.985 [Thread-170] WARN  o.s.b.f.s.DisposableBeanAdapter - Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-07-26 10:00:40.029 [Thread-170] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-5} closing ...
2025-07-26 10:00:40.030 [Thread-170] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-5} closed
2025-07-26 10:00:46.398 [restartedMain] INFO  org.scrm.ScrmSystemApplication - The following 1 profile is active: "dev"
2025-07-26 10:00:46.410 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 10:00:46.410 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-26 10:00:46.410 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-26 10:00:46.410 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-26 10:00:46.410 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-26 10:00:46.410 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-26 10:00:46.410 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-26 10:00:48.474 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 10:00:48.474 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 10:00:48.629 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 154 ms. Found 0 Redis repository interfaces.
2025-07-26 10:00:48.890 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=bbaafe60-3136-36a4-aeea-fca110465031
2025-07-26 10:00:48.998 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$e9561e62] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:00:49.019 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:00:49.024 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:00:49.026 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:00:49.028 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:00:49.032 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:00:49.034 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:00:49.035 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$527/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:00:49.052 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:00:49.058 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:00:49.066 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:00:49.078 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 10:00:49.443 [restartedMain] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-26 10:00:49.776 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7880 (http)
2025-07-26 10:00:49.776 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 10:00:49.776 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-26 10:00:49.789 [restartedMain] INFO  o.a.c.c.C.[Tomcat-2].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-26 10:00:49.789 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3379 ms
2025-07-26 10:00:50.417 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-26 10:00:51.620 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-6} inited
2025-07-26 10:01:02.468 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-26 10:01:02.541 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:03.010 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:03.045 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:03.575 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:03.921 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:04.152 [restartedMain] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-26 10:01:04.169 [redisson-netty-35-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 10:01:04.176 [redisson-netty-35-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-26 10:01:04.684 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:04.849 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:04.856 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:06.372 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:06.780 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:06.833 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:08.209 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:10.070 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:10.452 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:11.029 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:11.080 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:12.544 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-26 10:01:13.494 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-26 10:01:13.494 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-26 10:01:13.837 [restartedMain] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure any request
2025-07-26 10:01:14.230 [restartedMain] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-26 10:01:14.402 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 10:01:14.402 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-26 10:01:14.402 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-26 10:01:14.510 [restartedMain] INFO  c.a.n.c.identify.CredentialWatcher - null No credential found
2025-07-26 10:01:14.549 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7880 (http) with context path ''
2025-07-26 10:01:14.550 [restartedMain] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=7880, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-system', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-26 10:01:14.550 [restartedMain] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-system with instance: Instance{instanceId='null', ip='**************', port=7880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-26 10:01:14.590 [restartedMain] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-system **************:7880 register finished
2025-07-26 10:01:14.775 [restartedMain] INFO  org.scrm.ScrmSystemApplication - Started ScrmSystemApplication in 30.358 seconds (JVM running for 1298.576)
2025-07-26 10:01:14.779 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system-dev.yml, group=DEFAULT_GROUP, cnt=4
2025-07-26 10:01:14.779 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=4
2025-07-26 10:01:14.779 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system, group=DEFAULT_GROUP, cnt=4
2025-07-26 10:01:14.779 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system.yml, group=DEFAULT_GROUP, cnt=4
2025-07-26 10:01:14.780 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-26 10:01:15.591 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-26 10:01:15.592 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
