<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.web.mapper.SysUserMapper">
    <resultMap type="org.scrm.base.core.domain.entity.SysUser" id="SysUserResult">
        <id property="userId" column="user_id"/>
        <result property="weUserId" column="we_user_id"/>
        <result property="dataScope" column="data_scope"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="position" column="position"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="sex" column="sex"/>
        <result property="email" column="email"/>
        <result property="bizMail" column="biz_mail"/>
        <result property="leader" column="leader"/>
        <result property="avatar" column="avatar"/>
        <result property="thumbAvatar" column="thumb_avatar"/>
        <result property="telephone" column="telephone"/>
        <result property="nickName" column="nick_name"/>
        <result property="extAttr" column="ext_attr"/>
        <result property="weUserStatus" column="we_user_status"/>
        <result property="qrCode" column="qr_code"/>
        <result property="externalProfile" column="external_profile"/>
        <result property="externalPosition" column="external_position"/>
        <result property="address" column="address"/>
        <result property="openUserid" column="open_userid"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="userType" column="user_type"/>
        <association property="dept" column="dept_id" javaType="org.scrm.base.core.domain.entity.SysDept"
                     resultMap="deptResult"/>
        <collection property="roles" javaType="java.util.List" resultMap="RoleResult"/>
    </resultMap>

    <resultMap id="userDeptResult" type="org.scrm.web.domain.vo.UserVo">
        <id property="userId" column="user_id"/>
        <result property="weUserId" column="we_user_id"/>
        <result property="scopeDept" column="scopeDept"/>
        <result property="dataScope" column="data_scope"/>
        <result property="userName" column="user_name"/>
        <result property="position" column="position"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="nickName" column="nick_name"/>
        <result property="account" column="account"/>
        <result property="openUserid" column="open_userid"/>
        <collection property="userDepts" ofType="org.scrm.base.core.domain.entity.SysUserDept">
            <id property="deptId" column="dept_id"/>
            <result property="deptName" column="dept_name"/>
            <result property="deptEnName" column="dept_en_name"/>
        </collection>
    </resultMap>

    <resultMap id="userRoleResult" type="org.scrm.web.domain.vo.UserRoleVo">
        <id property="userId" column="user_id"/>
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
    </resultMap>

    <resultMap id="deptResult" type="org.scrm.base.core.domain.entity.SysDept">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="deptEnName" column="dept_en_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="status" column="dept_status"/>
    </resultMap>

    <resultMap id="RoleResult" type="org.scrm.base.core.domain.entity.SysRole">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="status" column="role_status"/>
    </resultMap>

    <sql id="simpleUserVo">
        select user_id,
               we_user_id,
               ifnull(data_scope, 5) as data_scope,
               dept_id,
               user_name,
               user_type,
               nick_name,
               email,
               phone_number,
               sex,
               avatar,
               password,
               status,
               del_flag,
               login_ip,
               login_date,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               we_user_id,
               position,
               thumb_avatar,
               biz_mail,
               leader,
               telephone,
               ext_attr,
               we_user_status,
               qr_code,
               external_profile,
               external_position,
               address,
               open_userid
        from sys_user
    </sql>

    <sql id="selectUserVo">
        select u.user_id,
               ifnull(u.data_scope, 5) as data_scope,
               u.we_user_id,
               u.dept_id,
               u.user_name,
               u.position,
               u.phone_number,
               u.sex,
               u.email,
               u.biz_mail,
               u.leader,
               u.avatar,
               u.thumb_avatar,
               u.telephone,
               u.nick_name,
               u.ext_attr,
               u.we_user_status,
               u.qr_code,
               u.external_profile,
               u.external_position,
               u.address,
               u.open_userid,
               u.password,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               u.user_type,
               d.dept_id,
               d.parent_id,
               d.dept_name,
               d.dept_en_name,
               d.status                as dept_status,
               r.role_id,
               r.role_name,
               r.role_key,
               r.role_sort,
               r.data_scope,
               r.status                as role_status
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
                 left join sys_user_role ur on u.user_id = ur.user_id
                 left join sys_role r on r.role_id = ur.role_id
    </sql>

    <select id="selectUserRoleList" parameterType="java.util.List" resultMap="userRoleResult">
        select u.user_id,
        r.role_id,
        r.role_name
        from sys_user u
        left join sys_user_role ur on u.user_id = ur.user_id and u.user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        left join sys_role r on r.role_id = ur.role_id
        where u.del_flag = '0' and r.role_id is not null
    </select>

    <select id="selectUserDeptList" resultMap="userDeptResult">
        select
        u.we_user_id,
        u.account,
        ifnull(u.data_scope,5) as data_scope,
        (SELECT GROUP_CONCAT(d.dept_name) FROM sys_user_manage_scop sums
        LEFT JOIN sys_dept d ON d.dept_id=sums.dept_id
        WHERE sums.user_id=u.user_id ) as scopeDept,
        u.user_id,
        u.user_name,
        u.position,
        u.phone_number,
        u.nick_name,
        u.open_userid,
        d.dept_id,
        d.dept_name,
        d.dept_en_name
        from sys_user u
        left join sys_user_dept ud on u.user_id = ud.user_id
        left join sys_dept d on ud.dept_id = d.dept_id
        left join sys_user_role sur on sur.user_id=u.user_id
        where u.is_user_leave=0
        <if test="sysUser.userName != null and sysUser.userName != ''">
            AND u.user_name like concat('%', #{sysUser.userName}, '%')
        </if>
          <if test="sysUser.defaultAccount != null">
               and u.default_account=#{sysUser.defaultAccount}
          </if>
        <if test="sysUser.status != null and sysUser.status != ''">
            AND u.status = #{sysUser.status}
        </if>
        <if test="sysUser.phoneNumber != null and sysUser.phoneNumber != ''">
            AND u.phone_number like concat('%', #{sysUser.phoneNumber}, '%')
        </if>
        <if test="sysUser.beginTime != null and sysUser.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{sysUser.beginTime},'%y%m%d')
        </if>
        <if test="sysUser.endTime != null and sysUser.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{sysUser.endTime},'%y%m%d')
        </if>
        <if test="sysUser.roleId != null ">
            AND sur.role_id = #{sysUser.roleId}
        </if>
        <choose>
            <when test="sysUser.checkIsRoot">
                <if test="sysUser.deptId != null">
                    AND (FIND_IN_SET(#{sysUser.deptId},d.ancestors) or d.dept_id=#{sysUser.deptId})
                </if>
            </when>
            <otherwise>
                <if test="sysUser.deptId != null">
                    AND ud.dept_id= #{sysUser.deptId}
                </if>
            </otherwise>
        </choose>
        AND u.del_flag = 0
        GROUP BY u.we_user_id
        <if test="pageDomain !=null">
            <if test="pageDomain.pageNum !=null and pageDomain.pageSize !=null">
                limit ${pageDomain.pageSize * (pageDomain.pageNum-1)}, #{pageDomain.pageSize}
            </if>
        </if>

    </select>

    <select id="selectCountUserDeptList" parameterType="org.scrm.base.core.domain.entity.SysUser" resultType="int">
        select
        count(DISTINCT u.we_user_id)
        from sys_user u
        left join sys_user_dept ud on u.user_id = ud.user_id
        left join sys_dept d on ud.dept_id = d.dept_id
        left join sys_user_role sur on sur.user_id=u.user_id
        where u.is_user_leave=0
        <if test="sysUser.userName != null and sysUser.userName != ''">
            AND u.user_name like concat('%', #{sysUser.userName}, '%')
        </if>
        <if test="sysUser.defaultAccount != null">
            and u.default_account=#{sysUser.defaultAccount}
        </if>
        <if test="sysUser.status != null and sysUser.status != ''">
            AND u.status = #{sysUser.status}
        </if>
        <if test="sysUser.phoneNumber != null and sysUser.phoneNumber != ''">
            AND u.phone_number like concat('%', #{sysUser.phoneNumber}, '%')
        </if>
        <if test="sysUser.beginTime != null and sysUser.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{sysUser.beginTime},'%y%m%d')
        </if>
        <if test="sysUser.endTime != null and sysUser.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{sysUser.endTime},'%y%m%d')
        </if>
        <if test="sysUser.roleId != null ">
            AND sur.role_id = #{sysUser.roleId}
        </if>
        <choose>
            <when test="sysUser.checkIsRoot">
                <if test="sysUser.deptId != null">
                    AND (FIND_IN_SET(#{sysUser.deptId},d.ancestors) or d.dept_id=#{sysUser.deptId})
                </if>
            </when>
            <otherwise>
                <if test="sysUser.deptId != null">
                    AND ud.dept_id= #{sysUser.deptId}
                </if>
            </otherwise>
        </choose>
        AND u.del_flag = 0
    </select>

    <select id="selectUserList" parameterType="org.scrm.base.core.domain.entity.SysUser"
            resultMap="SysUserResult">
        select u.user_id,u.we_user_id,u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phone_number,
        u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
        d.dept_name, d.leader from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        where u.del_flag = '0'
        <if test="userId != null">
            AND u.user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="phoneNumber != null and phoneNumber != ''">
            AND u.phone_number like concat('%', #{phoneNumber}, '%')
        </if>
        <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        <if test="deptId != null and deptId != 0">
            AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
            (#{deptId},ancestors) ))
        </if>
    </select>

    <select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = 0
        AND u.user_name = #{userName}
    </select>

    <select id="selectUserByAccount" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = 0
        AND u.account = #{account}
    </select>

    <select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_id = #{userId}
    </select>

    <select id="checkUserNameUnique" parameterType="String" resultType="int">
        select count(1)
        from sys_user
        where user_name = #{userName}
          and del_flag = '0'
        limit 1
    </select>

    <select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
        select user_id, phone_number
        from sys_user
        where phone_number = #{phoneNumber}
          and del_flag = '0'
        limit 1
    </select>

    <select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
        select user_id, email
        from sys_user
        where email = #{email}
          and del_flag = '0'
        limit 1
    </select>

    <select id="selectUserByWeUserId" resultMap="SysUserResult">
        <include refid="simpleUserVo"/>
        <where>
            <if test="weUserId != null and weUserId != ''">
                and we_user_id = #{weUserId}
            </if>
            and del_flag = 0
        </where>
    </select>

    <insert id="insertUser" parameterType="org.scrm.base.core.domain.entity.SysUser" useGeneratedKeys="true"
            keyProperty="userId">
        insert into sys_user(
        <if test="userId != null and userId != 0">user_id,</if>
        <if test="weUserId != null and weUserId != ''">we_user_id,</if>
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="userName != null and userName != ''">user_name,</if>
        <if test="position != null and position != ''">position,</if>
        <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
        <if test="sex != null and sex != ''">sex,</if>
        <if test="email != null and email != ''">email,</if>
        <if test="bizMail != null and bizMail != ''">biz_mail,</if>
        <if test="leader != null and leader != ''">leader,</if>
        <if test="avatar != null and avatar != ''">avatar,</if>
        <if test="thumbAvatar != null and thumbAvatar != ''">thumb_avatar,</if>
        <if test="telephone != null and telephone != ''">telephone,</if>
        <if test="nickName != null and nickName != ''">nick_name,</if>
        <if test="extAttr != null and extAttr != ''">ext_attr,</if>
        <if test="weUserStatus != null and weUserStatus != ''">we_user_status,</if>
        <if test="qrCode != null and qrCode != ''">qr_code,</if>
        <if test="externalProfile != null and externalProfile != ''">external_profile,</if>
        <if test="externalPosition != null and externalPosition != ''">external_position,</if>
        <if test="address != null and address != ''">address,</if>
        <if test="openUserid != null and openUserid != ''">open_userid,</if>
        <if test="password != null and password != ''">password,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="userType != null and userType != ''">user_type,</if>
        create_time
        )values(
        <if test="userId != null and userId != 0">#{userId},</if>
        <if test="weUserId != null and weUserId != ''">#{weUserId},</if>
        <if test="deptId != null and deptId != 0">#{deptId},</if>
        <if test="userName != null and userName != ''">#{userName},</if>
        <if test="position != null and position != ''">#{position},</if>
        <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
        <if test="sex != null and sex != ''">#{sex},</if>
        <if test="email != null and email != ''">#{email},</if>
        <if test="bizMail != null and bizMail != ''">#{bizMail},</if>
        <if test="leader != null and leader != ''">#{leader},</if>
        <if test="avatar != null and avatar != ''">#{avatar},</if>
        <if test="thumbAvatar != null and thumbAvatar != ''">#{thumbAvatar},</if>
        <if test="telephone != null and telephone != ''">#{telephone},</if>
        <if test="nickName != null and nickName != ''">#{nickName},</if>
        <if test="extAttr != null and extAttr != ''">#{extAttr},</if>
        <if test="weUserStatus != null and weUserStatus != ''">#{weUserStatus},</if>
        <if test="qrCode != null and qrCode != ''">#{qrCode},</if>
        <if test="externalProfile != null and externalProfile != ''">#{externalProfile},</if>
        <if test="externalPosition != null and externalPosition != ''">#{externalPosition},</if>
        <if test="address != null and address != ''">#{address},</if>
        <if test="openUserid != null and openUserid != ''">#{openUserid},</if>
        <if test="password != null and password != ''">#{password},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="userType != null and userType != ''">#{userType},</if>
        sysdate()
        )
    </insert>

    <update id="updateUser" parameterType="org.scrm.base.core.domain.entity.SysUser">
        update sys_user
        <set>
            <if test="weUserId != null and weUserId != ''">we_user_id = #{weUserId},</if>
            <if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="position != null and position != ''">position = #{position},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="bizMail != null and bizMail != ''">biz_mail = #{bizMail},</if>
            <if test="leader != null and leader != ''">leader = #{leader},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="thumbAvatar != null and thumbAvatar != ''">thumb_avatar = #{thumbAvatar},</if>
            <if test="telephone != null and telephone != ''">telephone = #{telephone},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="extAttr != null and extAttr != ''">ext_attr = #{extAttr},</if>
            <if test="weUserStatus != null and weUserStatus != ''">we_user_status = #{weUserStatus},</if>
            <if test="qrCode != null and qrCode != ''">qr_code = #{qrCode},</if>
            <if test="externalProfile != null and externalProfile != ''">external_profile = #{externalProfile},</if>
            <if test="externalPosition != null and externalPosition != ''">external_position = #{externalPosition},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="openUserid != null and openUserid != ''">open_userid = #{openUserid},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="userType != null and userType != ''">user_type = #{userType},</if>
            update_time = sysdate()
        </set>
        where user_id = #{userId}
    </update>

    <update id="updateUserStatus" parameterType="org.scrm.base.core.domain.entity.SysUser">
        update sys_user
        set status = #{status}
        where user_id = #{userId}
    </update>

    <update id="updateUserAvatar" parameterType="org.scrm.base.core.domain.entity.SysUser">
        update sys_user
        set avatar = #{avatar}
        where user_name = #{userName}
    </update>

    <update id="resetUserPwd" parameterType="org.scrm.base.core.domain.entity.SysUser">
        update sys_user
        set password = #{password}
        where user_name = #{userName}
    </update>

    <delete id="deleteUserById" parameterType="Long">
        delete
        from sys_user
        where user_id = #{userId}
    </delete>

    <delete id="deleteUserByIds" parameterType="Long">
        update sys_user set del_flag = '2' where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <update id="batchAddOrUpdate">
        insert into sys_user (user_id, dept_id, user_name, user_type, nick_name, email, phone_number, sex,
        avatar,password,status,login_ip,login_date,we_user_id,position, thumb_avatar,
        biz_mail, leader, telephone, ext_attr, we_user_status,qr_code, external_profile,
        external_position,address,open_userid,is_allocate,dimission_time,is_open_chat,
        create_by,create_by_id,create_time,update_by,update_by_id,update_time,remark)
        values
        <foreach collection="weUserList" item="weUser" index="index" separator=",">
            (
            #{weUser.userId},#{weUser.deptId},#{weUser.userName},#{weUser.userType},#{weUser.nickName},#{weUser.email},#{weUser.phoneNumber},#{weUser.sex},
            #{weUser.avatar}, #{weUser.password}, #{weUser.status}, #{weUser.loginIp}, #{weUser.loginDate},
            #{weUser.weUserId}, #{weUser.position}, #{weUser.thumbAvatar},
            #{weUser.bizMail}, #{weUser.leader}, #{weUser.telephone}, #{weUser.extAttr}, #{weUser.weUserStatus},
            #{weUser.qrCode}, #{weUser.externalProfile},
            #{weUser.externalPosition}, #{weUser.address}, #{weUser.openUserid}, #{weUser.isAllocate},
            #{weUser.dimissionTime}, #{weUser.isOpenChat},
            #{weUser.createBy}, #{weUser.createById}, #{weUser.createTime}, #{weUser.updateBy}, #{weUser.updateById},
            #{weUser.updateTime}, #{weUser.remark}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        dept_id=IFNULL(VALUES(dept_id), sys_user.dept_id),
        user_name=IFNULL(VALUES(user_name), sys_user.user_name),
        user_type=IFNULL(VALUES(user_type), sys_user.user_type),
        nick_name=IFNULL(VALUES(nick_name), sys_user.nick_name),
        email=IFNULL(VALUES(email), sys_user.email),
        phone_number=IFNULL(VALUES(phone_number), sys_user.phone_number),
        sex=IFNULL(VALUES(sex), sys_user.sex),
        avatar=IFNULL(VALUES(avatar), sys_user.avatar),
        password=IFNULL(VALUES(password), sys_user.password),
        status=IFNULL(VALUES(status), sys_user.status),
        login_ip=IFNULL(VALUES(login_ip), sys_user.login_ip),
        login_date=IFNULL(VALUES(login_date), sys_user.login_date),
        we_user_id=IFNULL(VALUES(we_user_id), sys_user.we_user_id),
        position=IFNULL(VALUES(position), sys_user.position),
        thumb_avatar=IFNULL(VALUES(thumb_avatar), sys_user.thumb_avatar),
        biz_mail=IFNULL(VALUES(biz_mail), sys_user.biz_mail),
        leader=IFNULL(VALUES(leader), sys_user.leader),
        telephone=IFNULL(VALUES(telephone), sys_user.telephone),
        ext_attr=IFNULL(VALUES(ext_attr), sys_user.ext_attr),
        we_user_status=IFNULL(VALUES(we_user_status), sys_user.we_user_status),
        qr_code=IFNULL(VALUES(qr_code), sys_user.qr_code),
        external_profile=IFNULL(VALUES(external_profile), sys_user.external_profile),
        external_position=IFNULL(VALUES(external_position), sys_user.external_position),
        address=IFNULL(VALUES(address), sys_user.address),
        open_userid=IFNULL(VALUES(open_userid), sys_user.open_userid),
        is_allocate=IFNULL(VALUES(is_allocate), sys_user.is_allocate),
        dimission_time=IFNULL(VALUES(dimission_time), sys_user.dimission_time),
        is_open_chat=IFNULL(VALUES(is_open_chat), sys_user.is_open_chat),
        update_by=IFNULL(VALUES(update_by), sys_user.update_by),
        update_by_id=IFNULL(VALUES(update_by_id), sys_user.update_by_id),
        update_time=IFNULL(VALUES(update_time), sys_user.update_time),
        remark=IFNULL(VALUES(remark), sys_user.remark);

    </update>

    <select id="findAllSysUser" resultType="org.scrm.base.core.domain.entity.SysUser">
        SELECT
        su.*,
        (SELECT GROUP_CONCAT(d.dept_name) FROM sys_dept d WHERE sud.dept_id = d.dept_id) as dept_name
        FROM
        sys_user su
        LEFT JOIN sys_user_dept sud on su.user_id=sud.user_id
        WHERE
        su.del_flag = 0 and sud.del_flag='0'
        <if test="(weUserIds !='' and weUserIds !=null) or (positions !='' and positions !=null) or (deptIds != null and deptIds != '')">
            AND (
                <trim prefixOverrides="OR">
                    <if test="weUserIds !='' and weUserIds !=null">
                        OR su.we_user_id IN
                        <foreach collection="weUserIds.split(',')" item="item" index="index" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    <if test="positions !='' and positions !=null">
                        OR su.position IN
                        <foreach collection="positions.split(',')" item="item" index="index" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    <if test="deptIds != null and deptIds != ''">
                        OR sud.dept_id IN
                        <foreach collection="deptIds.split(',')" item="item" index="index" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>
                </trim>
            )
        </if>

    </select>


    <resultMap id="userListByQueryResult" type="org.scrm.domain.system.user.vo.SysUserVo">
        <result property="userId" column="user_id"/>
        <result property="weUserId" column="we_user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="position" column="position"/>
        <result property="userType" column="user_type"/>
        <result property="nickName" column="nick_name"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="thumbAvatar" column="thumb_avatar"/>
        <result property="weUserStatus" column="we_user_status"/>
        <result property="openUserid" column="open_userid"/>
        <result property="kfStatus" column="kf_status"/>
        <collection property="deptList" ofType="org.scrm.domain.system.dept.vo.SysDeptVo">
            <result property="deptId" column="sdept_id"/>
            <result property="parentId" column="parent_id"/>
            <result property="deptName" column="dept_name"/>
        </collection>
    </resultMap>

    <select id="getUserListByQuery" resultMap="userListByQueryResult">
        select
        su.user_id,
        su.dept_id,
        su.user_name,
        su.user_type,
        su.nick_name,
        su.sex,
        su.avatar,
        su.status,
        su.we_user_id,
        su.position,
        su.thumb_avatar,
        su.we_user_status,
        su.address,
        su.open_userid,
        su.is_allocate,
        su.dimission_time,
        su.is_open_chat,
        su.kf_status,
        sd.dept_id as sdept_id,
        sd.parent_id,
        sd.dept_name


        from sys_user su

        left join sys_user_dept sud on sud.user_id = su.user_id and sud.del_flag = 0
        left join sys_dept sd on sd.dept_id = sud.dept_id and sd.del_flag = 0
        left join sys_user_role sr on sr.user_id = su.user_id
        <where>
            and su.del_flag = 0
            <if test="userId != null">
                and su.user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                and su.user_name like concat('%', #{userName}, '%')
            </if>
            <if test="weUserId != null and weUserId != ''">
                and su.we_user_id = #{weUserId}
            </if>
            <if test="weUserIds != null and weUserIds.size() > 0">
                and su.we_user_id in
                <foreach item="weUserId" collection="weUserIds" index="index" open="(" separator="," close=")">
                    #{weUserId}
                </foreach>
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and sud.dept_id in
                <foreach item="deptId" collection="deptIds" index="index" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="roleIds != null and roleIds.size() > 0">
                and sr.role_id in
                <foreach item="roleId" collection="roleIds" index="index" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
            <if test="positions != null and positions.size() > 0">
                and su.position in
                <foreach item="position" collection="positions" index="index" open="(" separator="," close=")">
                    #{position}
                </foreach>
            </if>
            <if test="weUserStatus != null">
                and su.we_user_status = #{weUserStatus}
            </if>
            <if test="isAllocate != null">
                and su.is_allocate = #{isAllocate}
            </if>
            <if test="isOpenChat != null">
                and su.is_open_chat = #{isOpenChat}
            </if>
            <if test="kfStatus != null">
                and su.kf_status = #{kfStatus}
            </if>
            <if test="sex != null">
                and su.sex = #{sex}
            </if>
        </where>
        GROUP BY su.we_user_id
    </select>

    <select id="findSysUserByDeptIds" resultType="org.scrm.base.core.domain.entity.SysUser">
        SELECT
            su.*
        FROM
            sys_user su
        LEFT JOIN sys_user_dept sud ON su.user_id=sud.user_id
        where  su.del_flag=0
          <if test="deptIds != null and deptIds.size()>0">
              and sud.dept_id in
              <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                  #{deptId}
              </foreach>
          </if>
    </select>

    <select id="findSysUserBySea" resultType="org.scrm.base.core.domain.entity.SysUser">
        SELECT
            su.*
        FROM
            sys_user su
        left join sys_user_dept sud on sud.user_id = su.user_id and sud.del_flag = 0
        <where>
            <if test="query.weUserIds != null and query.weUserIds.size()>0">
               and su.we_user_id in
                <foreach collection="query.weUserIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.positions != null and query.positions.size()>0">
                and su.position in
                <foreach collection="query.positions" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.deptIds != null and query.deptIds.size()>0">
                and sud.dept_id in
                <foreach collection="query.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>

        </where>
    </select>
</mapper> 