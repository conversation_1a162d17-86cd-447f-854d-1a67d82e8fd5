
34481a7e0be905bbe7996dd5434917800843e721	{"key":"{\"terser\":\"4.8.1\",\"node_version\":\"v18.20.8\",\"terser-webpack-plugin\":\"1.4.6\",\"terser-webpack-plugin-options\":{\"test\":new RegExp(\"\\\\.m?js(\\\\?.*)?$\", \"i\"),\"chunkFilter\":() => true,\"warningsFilter\":() => true,\"extractComments\":false,\"sourceMap\":false,\"cache\":true,\"cacheKeys\":defaultCacheKeys => defaultCacheKeys,\"parallel\":true,\"include\":undefined,\"exclude\":undefined,\"minify\":undefined,\"terserOptions\":{\"output\":{\"comments\":new RegExp(\"^\\\\**!|@preserve|@license|@cc_on\", \"i\")},\"compress\":{\"arrows\":false,\"collapse_vars\":false,\"comparisons\":false,\"computed_props\":false,\"hoist_funs\":false,\"hoist_props\":false,\"hoist_vars\":false,\"inline\":false,\"loops\":false,\"negate_iife\":false,\"properties\":false,\"reduce_funcs\":false,\"reduce_vars\":false,\"switches\":false,\"toplevel\":false,\"typeofs\":false,\"booleans\":true,\"if_return\":true,\"sequences\":true,\"unused\":true,\"conditionals\":true,\"dead_code\":true,\"evaluate\":true,\"drop_console\":false,\"drop_debugger\":true},\"mangle\":{\"safari10\":true}}},\"hash\":\"9046375756a26f5b5ecfcd9017dd88fb\"}","integrity":"sha512-T+nd05QefwptQkmZyGsqdtQ2m/XpVKXNGv1E0RYYgei1MNLAHQnIGV9FaQO+VJg2kYtwFKW5QAtbOZOywnMWHw==","time":1753431666268,"size":336651}