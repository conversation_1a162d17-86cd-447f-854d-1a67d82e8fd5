{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue", "mtime": 1753490486084}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751130701171}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751130703090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}