<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.web.mapper.SysRoleDeptMapper">

	<resultMap type="org.scrm.web.domain.SysRoleDept" id="SysRoleDeptResult">
		<result property="roleId"     column="role_id"      />
		<result property="deptId"     column="dept_id"      />
	</resultMap>

	<resultMap id="deptResult" type="org.scrm.base.core.domain.entity.SysDept">
		<id property="deptId" column="dept_id"/>
		<result property="parentId" column="parent_id"/>
		<result property="deptName" column="dept_name"/>
		<result property="deptEnName" column="dept_en_name"/>
		<result property="orderNum" column="order_num"/>
		<result property="leader" column="leader"/>
		<result property="status" column="dept_status"/>
	</resultMap>

	<select id="selectRoleDeptList" parameterType="Long" resultMap="deptResult">
		select
		    d.dept_id,
		    d.dept_name,
		    d.dept_en_name,
		    d.order_num,
		    d.leader,
		    d.status
		from sys_role_dept rd
		left join sys_dept d on d.dept_id = rd.dept_id
		where role_id=#{roleId}
	</select>

	<delete id="deleteRoleDeptByRoleId" parameterType="Long">
		delete from sys_role_dept where role_id=#{roleId}
	</delete>
	
	<select id="selectCountRoleDeptByDeptId" resultType="Integer">
	    select count(1) from sys_role_dept where dept_id=#{deptId}
	</select>
	
	<delete id="deleteRoleDept" parameterType="Long">
 		delete from sys_role_dept where role_id in
 		<foreach collection="array" item="roleId" open="(" separator="," close=")">
 			#{roleId}
        </foreach> 
 	</delete>
	
	<insert id="batchRoleDept">
		insert into sys_role_dept(role_id, dept_id) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.roleId},#{item.deptId})
		</foreach>
	</insert>
	
</mapper> 