# 拨打电话SOP动态标签修复测试方案

## 修复内容总结

### 1. 问题分析
- **客户重复添加问题**：拨打电话SOP在动态标签重新评估时，没有经过重复检查逻辑，直接调用 `builderExecuteCustomerSopPlan`
- **执行计划缺失问题**：拨打电话SOP的时间计算逻辑可能导致某些时间段的执行计划没有正确生成

### 2. 修复方案
1. **增强重复检查逻辑**：在 `builderExecuteCustomerSopPlan` 方法中为动态标签重新评估添加重复检查
2. **优化时间计算逻辑**：确保拨打电话SOP在动态标签触发时使用正确的基准时间
3. **增强日志记录**：添加详细的调试日志，便于问题排查

### 3. 修复的关键代码变更

#### 3.1 evaluateAndAddCustomerToSop 方法增强
```java
// 对于拨打电话SOP，记录更详细的信息
if (sopBase.getBusinessType() != null && sopBase.getBusinessType().equals(7)) {
    log.info("makeLabel-拨打电话SOP重复检查：客户 {} 已存在于SOP {} 的执行计划中，executeTargetId: {}", 
            customer.getExternalUserid(), sopBase.getId(), existingTarget.getId());
}
```

#### 3.2 builderExecuteCustomerSopPlan 方法重复检查
```java
// 对于动态标签重新评估的情况，需要检查是否已存在执行目标，避免重复添加
if (!isCreateOrUpdate1) {
    WeSopExecuteTarget existingTarget = iWeSopExecuteTargetService.getOne(new LambdaQueryWrapper<WeSopExecuteTarget>()
            .eq(WeSopExecuteTarget::getSopBaseId, weSopBase.getId())
            .eq(WeSopExecuteTarget::getTargetId, weCustomer.getExternalUserid())
            .eq(WeSopExecuteTarget::getTargetType, 1) // 客户类型
            .eq(WeSopExecuteTarget::getExecuteWeUserId, k) // 执行员工
            .eq(WeSopExecuteTarget::getExecuteState, SopExecuteStatus.SOP_STATUS_ING.getType()) // 只检查进行中的
    );
    
    if (existingTarget != null) {
        shouldAdd = false;
        log.info("makeLabel-builderExecuteCustomerSopPlan：客户 {} 已在SOP {} 的执行计划中，跳过重复添加，executeTargetId: {}", 
                weCustomer.getExternalUserid(), weSopBase.getId(), existingTarget.getId());
    }
}
```

#### 3.3 时间计算逻辑优化
```java
// 确定计算基准时间：
// 1. 拨打电话SOP：对于动态标签触发的重新评估，使用当前时间；对于初始创建，使用客户添加时间
// 2. 其他SOP：使用客户添加时间
Date baseTime;
if (weSopBase.getBusinessType() != null && weSopBase.getBusinessType().equals(7)) {
    // 拨打电话SOP：如果是动态标签触发（!isCreateOrUpdate1 && buildXkSopPlan），使用当前时间
    if (!isCreateOrUpdate1 && buildXkSopPlan) {
        baseTime = new Date(); // 动态标签触发，使用当前时间
        log.info("makeLabel-拨打电话SOP动态标签触发，使用当前时间作为基准时间：{}", baseTime);
    } else {
        baseTime = weSopExecuteTarget.getAddCustomerOrCreateGoupTime(); // 初始创建，使用客户添加时间
        log.info("makeLabel-拨打电话SOP初始创建，使用客户添加时间作为基准时间：{}", baseTime);
    }
} else {
    baseTime = weSopExecuteTarget.getAddCustomerOrCreateGoupTime(); // 其他SOP使用客户添加时间
}
```

## 测试方案

### 测试场景1：客户重复添加问题验证
1. **前置条件**：
   - 创建一个拨打电话SOP，设置标签条件为"测试标签"
   - 客户A已经有"测试标签"，已在该SOP的执行计划中

2. **测试步骤**：
   - 给客户A再次添加"测试标签"（或添加其他标签后再添加"测试标签"）
   - 触发动态标签重新评估

3. **预期结果**：
   - 客户A不会被重复添加到SOP执行计划中
   - 日志中显示"跳过重复添加"的信息
   - 数据库中该客户在该SOP中只有一条执行目标记录

### 测试场景2：执行计划生成验证
1. **前置条件**：
   - 创建一个拨打电话SOP，设置多个时间段（如第1天、第3天、第7天）
   - 客户B没有"测试标签"

2. **测试步骤**：
   - 给客户B添加"测试标签"
   - 触发动态标签重新评估

3. **预期结果**：
   - 客户B被添加到SOP执行计划中
   - 为客户B生成所有时间段的执行计划（3条记录）
   - 时间计算基于当前时间而不是客户添加时间

### 测试场景3：标签移除后的清理验证
1. **前置条件**：
   - 客户C已在拨打电话SOP的执行计划中

2. **测试步骤**：
   - 移除客户C的"测试标签"，使其不再符合SOP条件
   - 触发动态标签重新评估

3. **预期结果**：
   - 客户C的SOP执行计划被设置为异常结束
   - 未执行的执行内容记录被删除
   - 日志中显示"客户因标签变更从SOP中移除"的信息

## 验证要点

### 1. 数据库验证
- 检查 `we_sop_execute_target` 表中是否有重复记录
- 检查 `we_sop_execute_target_attachments` 表中的执行计划数量是否正确
- 验证时间计算是否正确

### 2. 日志验证
- 查看是否有重复添加的警告日志
- 确认时间基准的选择日志
- 验证SOP重新评估的完整流程日志

### 3. 功能验证
- 确认拨打电话SOP的前端显示正常
- 验证客户列表中没有重复的客户
- 确认时间段显示正确

## 注意事项

1. **测试环境**：建议在测试环境进行完整测试后再部署到生产环境
2. **数据备份**：测试前备份相关数据表
3. **监控日志**：密切关注修复后的日志输出，确保逻辑正确
4. **性能影响**：验证重复检查逻辑不会显著影响性能
