<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a62e8931-58e2-44ce-a794-0241213733a1" name="Changes" comment="完善拨打电话SOP逻辑">
      <change beforePath="$PROJECT_DIR$/config/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/config/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scrm-module/scrm-business/src/main/java/org/scrm/service/impl/WeSopExecuteTargetServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/scrm-module/scrm-business/src/main/java/org/scrm/service/impl/WeSopExecuteTargetServiceImpl.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master-新增拨打电话SOP" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\devTool\maven\apache-maven-3.6.3" />
        <option name="localRepository" value="D:\devTool\maven\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\devTool\maven\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="jdkForImporter" value="1.8" />
        <option name="workspaceImportForciblyTurnedOn" value="true" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="1.8" />
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2yiNSi532Ee90Xepc8cyxaRshv9" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "Maven.scrm-api [clean].executor": "Run",
    "Maven.scrm-api [install].executor": "Run",
    "Maven.scrm-api [validate].executor": "Run",
    "Maven.scrm-api [verify].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.ScrmCallBackApplication.executor": "Debug",
    "Spring Boot.ScrmFileApplication.executor": "Debug",
    "Spring Boot.ScrmGatewayApplication.executor": "Debug",
    "Spring Boot.ScrmMobileApiApplication.executor": "Debug",
    "Spring Boot.ScrmOpenAiApplication.executor": "Debug",
    "Spring Boot.ScrmSystemApplication.executor": "Debug",
    "Spring Boot.ScrmTaskApplication.executor": "Debug",
    "Spring Boot.ScrmWeApiApplication.executor": "Debug",
    "Spring Boot.ScrmWeChatApiApplication.executor": "Debug",
    "StatusDashboardGroupingRule": "true",
    "git-widget-placeholder": "master-SOP添加编辑按钮",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/project/scrm/front",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "reference.settings.project.maven.importing",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\project\scrm\api\file\sql" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.ScrmSystemApplication">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="api" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="api" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ScrmCallBackApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="scrm-callback" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.scrm.ScrmCallBackApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ScrmFileApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="scrm-file" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.scrm.ScrmFileApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ScrmGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="scrm-gateway" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.scrm.ScrmGatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ScrmMobileApiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="scrm-mobile-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.scrm.ScrmMobileApiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ScrmOpenAiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="scrm-openai" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.scrm.ScrmOpenAiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ScrmSystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="scrm-system" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.scrm.ScrmSystemApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ScrmTaskApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="scrm-task" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.scrm.scheduler.ScrmTaskApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ScrmWeApiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="scrm-web-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.scrm.ScrmWeApiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ScrmWeChatApiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="scrm-wechat-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.scrm.wecom.ScrmWeChatApiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a62e8931-58e2-44ce-a794-0241213733a1" name="Changes" comment="" />
      <created>1750313436249</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750313436249</updated>
      <workItem from="1750313438479" duration="1030000" />
      <workItem from="1750314566509" duration="1363000" />
      <workItem from="1750315977629" duration="924000" />
      <workItem from="1750316915434" duration="16816000" />
      <workItem from="1750657893873" duration="23389000" />
      <workItem from="1751080338788" duration="2740000" />
      <workItem from="1751090768306" duration="597000" />
      <workItem from="1751094350839" duration="11692000" />
      <workItem from="1751246566180" duration="18018000" />
      <workItem from="1751358333347" duration="28191000" />
      <workItem from="1751503355181" duration="3682000" />
      <workItem from="1751507228722" duration="49345000" />
      <workItem from="1751854150902" duration="1680000" />
      <workItem from="1751856673881" duration="1817000" />
      <workItem from="1751858547005" duration="147000" />
      <workItem from="1751858703752" duration="8453000" />
      <workItem from="1752030201481" duration="5213000" />
      <workItem from="1752108437003" duration="34402000" />
      <workItem from="1752208876504" duration="53128000" />
      <workItem from="1752567227941" duration="31000" />
      <workItem from="1752567278260" duration="1169000" />
      <workItem from="1752585185473" duration="1881000" />
      <workItem from="1752636906311" duration="5610000" />
      <workItem from="1752649692601" duration="25314000" />
      <workItem from="1752802829634" duration="14588000" />
      <workItem from="1753088484089" duration="39873000" />
      <workItem from="1753262957405" duration="7187000" />
      <workItem from="1753337841133" duration="33618000" />
    </task>
    <task id="LOCAL-00001" summary="添加部分saas代码">
      <option name="closed" value="true" />
      <created>1750669456931</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750669456931</updated>
    </task>
    <task id="LOCAL-00002" summary="添加搜索标签名的代码">
      <option name="closed" value="true" />
      <created>1750986298006</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1750986298006</updated>
    </task>
    <task id="LOCAL-00003" summary="完善搜索标签名的代码">
      <option name="closed" value="true" />
      <created>1751252657162</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1751252657162</updated>
    </task>
    <task id="LOCAL-00004" summary="添加部分修改sop动态逻辑代码">
      <option name="closed" value="true" />
      <created>1751359301256</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1751359301256</updated>
    </task>
    <task id="LOCAL-00005" summary="一客一群修好问题修复">
      <option name="closed" value="true" />
      <created>1751360263049</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1751360263049</updated>
    </task>
    <task id="LOCAL-00006" summary="完善sop动态逻辑修改">
      <option name="closed" value="true" />
      <created>1751424609878</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1751424609878</updated>
    </task>
    <task id="LOCAL-00007" summary="还原注释的代码">
      <option name="closed" value="true" />
      <created>1751425010551</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1751425010552</updated>
    </task>
    <task id="LOCAL-00008" summary="删除无用的代码">
      <option name="closed" value="true" />
      <created>1751426180940</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1751426180940</updated>
    </task>
    <task id="LOCAL-00009" summary="完善修改sop动态逻辑code">
      <option name="closed" value="true" />
      <created>1751509091787</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1751509091787</updated>
    </task>
    <task id="LOCAL-00010" summary="修改客户SOP推送消息时间为T+0">
      <option name="closed" value="true" />
      <created>1751599675021</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1751599675021</updated>
    </task>
    <task id="LOCAL-00011" summary="添加拨打电话SOP功能代码">
      <option name="closed" value="true" />
      <created>1751617462560</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1751617462560</updated>
    </task>
    <task id="LOCAL-00012" summary="添加日志">
      <option name="closed" value="true" />
      <created>1752137566763</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1752137566763</updated>
    </task>
    <task id="LOCAL-00013" summary="添加部分移除标签logic">
      <option name="closed" value="true" />
      <created>1752195939885</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1752195939885</updated>
    </task>
    <task id="LOCAL-00014" summary="添加日志定位问题">
      <option name="closed" value="true" />
      <created>1752199729895</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1752199729895</updated>
    </task>
    <task id="LOCAL-00015" summary="修改批量打标签异常问题">
      <option name="closed" value="true" />
      <created>1752212306725</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1752212306725</updated>
    </task>
    <task id="LOCAL-00016" summary="完善日志+还原代码">
      <option name="closed" value="true" />
      <created>1752213470982</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1752213470982</updated>
    </task>
    <task id="LOCAL-00017" summary="添加通讯录回调接口">
      <option name="closed" value="true" />
      <created>1752463294011</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1752463294011</updated>
    </task>
    <task id="LOCAL-00018" summary="完善回调接口代码">
      <option name="closed" value="true" />
      <created>1752475398351</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1752475398351</updated>
    </task>
    <task id="LOCAL-00019" summary="添加移除标签逻辑">
      <option name="closed" value="true" />
      <created>1752567429674</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1752567429679</updated>
    </task>
    <task id="LOCAL-00020" summary="完善回调接口">
      <option name="closed" value="true" />
      <created>1752653812723</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1752653812723</updated>
    </task>
    <task id="LOCAL-00021" summary="完善拨打电话SOP代码">
      <option name="closed" value="true" />
      <created>1752723611045</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1752723611045</updated>
    </task>
    <task id="LOCAL-00022" summary="完善拨打电话SOP代码">
      <option name="closed" value="true" />
      <created>1752830361913</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1752830361913</updated>
    </task>
    <task id="LOCAL-00023" summary="完善拨打电话SOP代码">
      <option name="closed" value="true" />
      <created>1753147278332</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1753147278332</updated>
    </task>
    <task id="LOCAL-00024" summary="完善拨打电话SOP代码">
      <option name="closed" value="true" />
      <created>1753163278050</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1753163278050</updated>
    </task>
    <task id="LOCAL-00025" summary="完善拨打电话SOP代码">
      <option name="closed" value="true" />
      <created>1753241579659</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1753241579659</updated>
    </task>
    <task id="LOCAL-00026" summary="完善拨打电话SOP代码">
      <option name="closed" value="true" />
      <created>1753317297246</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1753317297246</updated>
    </task>
    <task id="LOCAL-00027" summary="完善SOP添加编辑按钮逻辑">
      <option name="closed" value="true" />
      <created>1753429273756</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1753429273756</updated>
    </task>
    <task id="LOCAL-00028" summary="完善拨打电话SOP逻辑">
      <option name="closed" value="true" />
      <created>1753490845911</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1753490845911</updated>
    </task>
    <option name="localTasksCounter" value="29" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master-新增拨打电话SOP" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="添加部分saas代码" />
    <MESSAGE value="添加搜索标签名的代码" />
    <MESSAGE value="完善搜索标签名的代码" />
    <MESSAGE value="添加部分修改sop动态逻辑代码" />
    <MESSAGE value="一客一群修好问题修复" />
    <MESSAGE value="完善sop动态逻辑修改" />
    <MESSAGE value="还原注释的代码" />
    <MESSAGE value="删除无用的代码" />
    <MESSAGE value="完善修改sop动态逻辑code" />
    <MESSAGE value="修改客户SOP推送消息时间为T+0" />
    <MESSAGE value="添加拨打电话SOP功能代码" />
    <MESSAGE value="添加日志" />
    <MESSAGE value="添加部分移除标签logic" />
    <MESSAGE value="添加日志定位问题" />
    <MESSAGE value="修改批量打标签异常问题" />
    <MESSAGE value="完善日志+还原代码" />
    <MESSAGE value="添加通讯录回调接口" />
    <MESSAGE value="完善回调接口代码" />
    <MESSAGE value="添加移除标签逻辑" />
    <MESSAGE value="完善回调接口" />
    <MESSAGE value="完善拨打电话SOP代码" />
    <MESSAGE value="完善SOP添加编辑按钮逻辑" />
    <MESSAGE value="完善拨打电话SOP逻辑" />
    <option name="LAST_COMMIT_MESSAGE" value="完善拨打电话SOP逻辑" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint type="java-exception">
          <properties class="java.sql.SQLSyntaxErrorException" package="java.sql" />
          <option name="timeStamp" value="13" />
        </breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/scrm-module/scrm-start/scrm-task/src/main/java/org/scrm/scheduler/service/impl/sop/SopTaskServiceImpl.java</url>
          <line>55</line>
          <option name="timeStamp" value="252" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/scrm-module/scrm-business/src/main/java/org/scrm/service/impl/WeSopBaseServiceImpl.java</url>
          <line>1756</line>
          <option name="timeStamp" value="253" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/scrm-module/scrm-business/src/main/java/org/scrm/service/impl/WeSopBaseServiceImpl.java</url>
          <line>1775</line>
          <option name="timeStamp" value="254" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/scrm-module/scrm-business/src/main/java/org/scrm/service/impl/WeSopExecuteTargetServiceImpl.java</url>
          <line>106</line>
          <option name="timeStamp" value="255" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/scrm-module/scrm-business/src/main/java/org/scrm/service/impl/WeSopBaseServiceImpl.java</url>
          <line>1772</line>
          <option name="timeStamp" value="256" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>