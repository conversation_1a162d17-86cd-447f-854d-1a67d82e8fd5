{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!D:\\project\\scrm\\front\\mobile2\\node_modules\\thread-loader\\dist\\cjs.js!D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue?vue&type=template&id=1a11559a&scoped=true", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue", "mtime": 1753489912752}, {"path": "D:\\project\\scrm\\front\\mobile2\\babel.config.js", "mtime": 1750650120014}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751130691203}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751130701171}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751130711384}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751130703090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uIHJlbmRlcigpIHsKICB2YXIgX3ZtID0gdGhpcywKICAgIF9jID0gX3ZtLl9zZWxmLl9jOwogIHJldHVybiBfYygnZGl2JywgewogICAgc3RhdGljQ2xhc3M6ICJzd2FybXNTT1AiCiAgfSwgW19jKCdkaXYnLCBbX2MoJ3Zhbi1zdGlja3knLCBbX2MoJ2RpdicsIHsKICAgIHN0YXRpY1N0eWxlOiB7CiAgICAgICJiYWNrZ3JvdW5kIjogIiNmYWZhZmEiCiAgICB9CiAgfSwgWyFfdm0uaXNQaG9uZUNhbGxTb3AgPyBfYygnZGl2JywgewogICAgc3RhdGljQ2xhc3M6ICJzd2FybXNTT1BfbWVzc2FnZSIKICB9LCBbX2MoJ2RpdicsIHsKICAgIHN0YXRpY0NsYXNzOiAic3dhcm1zU09QX21lc3NhZ2VfdG9wIgogIH0sIFtfYygnZGl2JywgewogICAgc3RhdGljQ2xhc3M6ICJzd2FybXNTT1BfbWVzc2FnZV90b3BfbGVmdCIKICB9LCBbX2MoJ2RpdicsIHsKICAgIHN0YXRpY0NsYXNzOiAic3dhcm1zU09QX21lc3NhZ2VfdG9wX2xlZnRfdXNlckltZyIKICB9LCBbX3ZtLmZvcm0uYXZhdGFyID8gX2MoJ2ltZycsIHsKICAgIGF0dHJzOiB7CiAgICAgICJzcmMiOiBfdm0uZm9ybS5hdmF0YXIsCiAgICAgICJhbHQiOiAiIgogICAgfQogIH0pIDogX3ZtLl9lKCldKSwgX2MoJ2RpdicsIHsKICAgIHN0YXRpY0NsYXNzOiAic3dhcm1zU09QX21lc3NhZ2VfdG9wX2xlZnRfaW5mbyIKICB9LCBbX2MoJ2RpdicsIFtfdm0uX3YoIiAiICsgX3ZtLl9zKF92bS5mb3JtLmN1c3RvbWVyTmFtZSkgKyAiICIpLCBfYygnc3BhbicsIHsKICAgIHN0eWxlOiB7CiAgICAgIGNvbG9yOiBfdm0uZm9ybS5jdXN0b21lclR5cGUgPT09IDEgPyAnIzRiZGUwMycgOiAnI2Y5YTkwYicKICAgIH0KICB9LCBbX3ZtLl92KCIgIiArIF92bS5fcyh7CiAgICAxOiAnQOW+ruS/oScsCiAgICAyOiBfdm0uZm9ybS5jb3JwTmFtZSA/ICdAJyArIF92bS5mb3JtLmNvcnBOYW1lIDogJ0DkvIHkuJrlvq7kv6EnCiAgfVtfdm0uZm9ybS5jdXN0b21lclR5cGVdKSArICIgIildKV0pLCBfYygnZGl2JywgW192bS5mb3JtLmdlbmRlciA9PT0gMSA/IF9jKCdpbWcnLCB7CiAgICBhdHRyczogewogICAgICAic3JjIjogcmVxdWlyZSgiLi4vLi4vLi4vLi4vYXNzZXRzL21hbi5wbmciKQogICAgfQogIH0pIDogX3ZtLmZvcm0uZ2VuZGVyID09PSAyID8gX2MoJ2ltZycsIHsKICAgIGF0dHJzOiB7CiAgICAgICJzcmMiOiByZXF1aXJlKCIuLi8uLi8uLi8uLi9hc3NldHMvd29tYW4ucG5nIikKICAgIH0KICB9KSA6IF92bS5fZSgpXSldKV0pLCBfYygnZGl2JywgewogICAgc3RhdGljQ2xhc3M6ICJzd2FybXNTT1BfbWVzc2FnZV90b3BfcmlnaHQiCiAgfSwgW19jKCdkaXYnLCB7CiAgICBzdGF0aWNDbGFzczogInRyYWNrLXN0YXRlIgogIH0sIFtfdm0uX3YoX3ZtLl9zKF92bS50cmFja1N0YXRlKSldKV0pXSldKSA6IF92bS5fZSgpLCBfYygnZGl2JywgewogICAgc3RhdGljQ2xhc3M6ICJzd2FybXNTT1BfdGFiQmFyIgogIH0sIFtfYygnZGl2JywgewogICAgY2xhc3M6IF92bS50YWJCYXIgPT0gMCA/ICdzd2FybXNTT1BfdGFiQmFyX2xpMScgOiAnc3dhcm1zU09QX3RhYkJhcl9saScsCiAgICBvbjogewogICAgICAiY2xpY2siOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgcmV0dXJuIF92bS5zZXRDaGFuZ2UoMCk7CiAgICAgIH0KICAgIH0KICB9LCBbX3ZtLl92KCIgIiArIF92bS5fcyhfdm0uaXNQaG9uZUNhbGxTb3AgPyAn5b6F5ouo5omTJyA6ICflvoXmjqjpgIEnKSArICIgIildKSwgX2MoJ2RpdicsIHsKICAgIGNsYXNzOiBfdm0udGFiQmFyID09IDEgPyAnc3dhcm1zU09QX3RhYkJhcl9saTEnIDogJ3N3YXJtc1NPUF90YWJCYXJfbGknLAogICAgb246IHsKICAgICAgImNsaWNrIjogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgIHJldHVybiBfdm0uc2V0Q2hhbmdlKDEpOwogICAgICB9CiAgICB9CiAgfSwgW192bS5fdigiICIgKyBfdm0uX3MoX3ZtLmlzUGhvbmVDYWxsU29wID8gJ+W3suaLqOaJkycgOiAn5bey5o6o6YCBJykgKyAiICIpXSldKV0pXSksIF9jKCdkaXYnLCB7CiAgICBzdGF0aWNDbGFzczogInN3YXJtc1NPUF9ib3giLAogICAgc3RhdGljU3R5bGU6IHsKICAgICAgImRpc3BsYXkiOiAiZmxleCIsCiAgICAgICJmbGV4LWRpcmVjdGlvbiI6ICJjb2x1bW4iCiAgICB9CiAgfSwgW192bS5pc1Bob25lQ2FsbFNvcCA/IF9jKCdkaXYnLCBbIV92bS5waG9uZUNhbGxDdXN0b21lcnMubGVuZ3RoID8gX2MoJ2RpdicsIHsKICAgIHN0YXRpY0NsYXNzOiAicGhvbmUtY2FsbC10aXAiCiAgfSwgW19jKCdkaXYnLCB7CiAgICBzdGF0aWNDbGFzczogInRpcC1pY29uIgogIH0sIFtfdm0uX3YoIvCfk54iKV0pLCBfYygnZGl2JywgewogICAgc3RhdGljQ2xhc3M6ICJ0aXAtdGV4dCIKICB9LCBbX3ZtLl92KCLmmoLml6Dmi6jmiZPnlLXor53ku7vliqEiKV0pLCBfYygnZGl2JywgewogICAgc3RhdGljQ2xhc3M6ICJ0aXAtZGVzYyIKICB9LCBbX3ZtLl92KCLlvZPmnInlrqLmiLfpnIDopoHnlLXor53ot5/ov5vml7bvvIzku7vliqHkvJrlnKjov5nph4zmmL7npLoiKV0pXSkgOiBfYygnZGl2JywgewogICAgc3RhdGljQ2xhc3M6ICJwaG9uZS1jYWxsLWN1c3RvbWVycyIKICB9LCBfdm0uX2woX3ZtLnBob25lQ2FsbEN1c3RvbWVycywgZnVuY3Rpb24gKGN1c3RvbWVyLCBpbmRleCkgewogICAgcmV0dXJuIF9jKCdkaXYnLCB7CiAgICAgIGtleTogaW5kZXgsCiAgICAgIHN0YXRpY0NsYXNzOiAiY3VzdG9tZXItaXRlbSIKICAgIH0sIFtfYygnZGl2JywgewogICAgICBzdGF0aWNDbGFzczogImN1c3RvbWVyLWluZm8iCiAgICB9LCBbX2MoJ2RpdicsIHsKICAgICAgc3RhdGljQ2xhc3M6ICJjdXN0b21lci1hdmF0YXIiCiAgICB9LCBbY3VzdG9tZXIuYXZhdGFyID8gX2MoJ2ltZycsIHsKICAgICAgYXR0cnM6IHsKICAgICAgICAic3JjIjogY3VzdG9tZXIuYXZhdGFyLAogICAgICAgICJhbHQiOiAi5aS05YOPIgogICAgICB9CiAgICB9KSA6IF9jKCdkaXYnLCB7CiAgICAgIHN0YXRpY0NsYXNzOiAiZGVmYXVsdC1hdmF0YXIiCiAgICB9LCBbX3ZtLl92KF92bS5fcyhjdXN0b21lci5jdXN0b21lck5hbWUgPyBjdXN0b21lci5jdXN0b21lck5hbWUuY2hhckF0KDApIDogJ+WuoicpKV0pXSksIF9jKCdkaXYnLCB7CiAgICAgIHN0YXRpY0NsYXNzOiAiY3VzdG9tZXItZGV0YWlscyIKICAgIH0sIFtfYygnZGl2JywgewogICAgICBzdGF0aWNDbGFzczogImN1c3RvbWVyLW5hbWUiCiAgICB9LCBbX3ZtLl92KF92bS5fcyhjdXN0b21lci5jdXN0b21lck5hbWUgfHwgJ+acquefpeWuouaItycpKV0pLCBfYygnZGl2JywgewogICAgICBzdGF0aWNDbGFzczogImN1c3RvbWVyLXBob25lIiwKICAgICAgc3RhdGljU3R5bGU6IHsKICAgICAgICAicG9pbnRlci1ldmVudHMiOiAibm9uZSIKICAgICAgfQogICAgfSwgW192bS5fdihfdm0uX3MoY3VzdG9tZXIuY3VzdG9tZXJQaG9uZSB8fCAn5pyq6K6+572u55S16K+dJykpXSksIF92bS50YWJCYXIgPT09IDEgJiYgY3VzdG9tZXIuY2FsbFRpbWUgPyBfYygnZGl2JywgewogICAgICBzdGF0aWNDbGFzczogImNhbGwtdGltZSIKICAgIH0sIFtfdm0uX3YoIiAiICsgX3ZtLl9zKF92bS5mb3JtYXRDYWxsVGltZShjdXN0b21lci5jYWxsVGltZSkpICsgIiAiKV0pIDogX3ZtLl9lKCldKV0pLCBfYygnZGl2JywgewogICAgICBzdGF0aWNDbGFzczogImN1c3RvbWVyLWFjdGlvbnMiCiAgICB9LCBbX2MoJ2RpdicsIHsKICAgICAgc3RhdGljQ2xhc3M6ICJhY3Rpb24tYnV0dG9ucyIKICAgIH0sIFtjdXN0b21lci5jdXN0b21lclBob25lID8gX2MoJ2RpdicsIHsKICAgICAgc3RhdGljQ2xhc3M6ICJwaG9uZS1jYWxsLWJ0biIsCiAgICAgIG9uOiB7CiAgICAgICAgImNsaWNrIjogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgcmV0dXJuIF92bS5tYWtlUGhvbmVDYWxsRm9yQ3VzdG9tZXIoY3VzdG9tZXIpOwogICAgICAgIH0KICAgICAgfQogICAgfSwgW192bS5fdigiIPCfk54g55S16K+dICIpXSkgOiBfYygnZGl2JywgewogICAgICBzdGF0aWNDbGFzczogInBob25lLWNhbGwtYnRuIGRpc2FibGVkIgogICAgfSwgW192bS5fdigiIOaXoOWPt+eggSAiKV0pLCBjdXN0b21lci5leHRlcm5hbFVzZXJpZCA/IF9jKCdkaXYnLCB7CiAgICAgIHN0YXRpY0NsYXNzOiAidm9pY2UtY2FsbC1idG4iLAogICAgICBvbjogewogICAgICAgICJjbGljayI6IGZ1bmN0aW9uICgkZXZlbnQpIHsKICAgICAgICAgIHJldHVybiBfdm0ubWFrZVZvaWNlQ2FsbEZvckN1c3RvbWVyKGN1c3RvbWVyKTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sIFtfdm0uX3YoIiDwn46kIOivremfsyAiKV0pIDogX3ZtLl9lKCldKV0pXSk7CiAgfSksIDApXSkgOiBfYygnZGl2JywgWyFfdm0uZGF0YUxpc3QubGVuZ3RoID8gX2MoJ05vRGF0YScpIDogX3ZtLl9sKF92bS5kYXRhTGlzdCwgZnVuY3Rpb24gKGl0ZW0sIGluZGV4KSB7CiAgICByZXR1cm4gX2MoJ2RpdicsIHsKICAgICAga2V5OiBpbmRleCwKICAgICAgc3RhdGljQ2xhc3M6ICJzd2FybXNTT1BfY29udGVudCIKICAgIH0sIFtfYygnZGl2JywgewogICAgICBjbGFzczogewogICAgICAgIHN3YXJtc1NPUF9jb250ZW50X3RvcDE6IGl0ZW0udHlwZSA9PT0gMSwKICAgICAgICBzd2FybXNTT1BfY29udGVudF90b3AyOiBpdGVtLnR5cGUgPT09IDIsCiAgICAgICAgc3dhcm1zU09QX2NvbnRlbnRfdG9wMzogaXRlbS50eXBlID09PSAzCiAgICAgIH0KICAgIH0sIFtfYygnZGl2JywgewogICAgICBzdGF0aWNDbGFzczogInN3YXJtc1NPUF9jb250ZW50X3RvcF90ZXh0IiwKICAgICAgc3RhdGljU3R5bGU6IHsKICAgICAgICAibWFyZ2luLWJvdHRvbSI6ICIxMnB4IgogICAgICB9CiAgICB9LCBbX3ZtLl92KF92bS5fcyhpdGVtLnNvcE5hbWUpKV0pLCBpdGVtLnR5cGUgPT09IDEgJiYgX3ZtLnRhYkJhciA9PT0gMCA/IF9jKCdkaXYnLCB7CiAgICAgIHN0YXRpY0NsYXNzOiAic3dhcm1zU09QX2NvbnRlbnRfdG9wX3RleHQiCiAgICB9LCBbX3ZtLl92KCIg6Led5o6o6YCB57uT5p2f5Ymp5L2ZIiArIF92bS5fcyhpdGVtLnRpbWUpICsgIiAiKV0pIDogX3ZtLl9lKCksIGl0ZW0udHlwZSA9PT0gMiAmJiBfdm0udGFiQmFyID09PSAwID8gX2MoJ2RpdicsIHsKICAgICAgc3RhdGljQ2xhc3M6ICJzd2FybXNTT1BfY29udGVudF90b3BfdGV4dCIKICAgIH0sIFtfdm0uX3YoIiDot53mjqjpgIHml7bpl7Tlt7Lov4ciICsgX3ZtLl9zKGl0ZW0udGltZSkgKyAiICIpXSkgOiBfdm0uX2UoKSwgaXRlbS50eXBlID09PSAzICYmIF92bS50YWJCYXIgPT09IDAgPyBfYygnZGl2JywgewogICAgICBzdGF0aWNDbGFzczogInN3YXJtc1NPUF9jb250ZW50X3RvcF90ZXh0IgogICAgfSwgW192bS5fdigi5pyq5Yiw5o6o6YCB5pe26Ze0IildKSA6IF92bS5fZSgpXSksIF9jKCdkaXYnLCB7CiAgICAgIHN0YXRpY0NsYXNzOiAic3dhcm1zU09QX2NvbnRlbnRfdGl0bGUiLAogICAgICBvbjogewogICAgICAgICJjbGljayI6IGZ1bmN0aW9uICgkZXZlbnQpIHsKICAgICAgICAgIGl0ZW0ub3BlbiA9ICFpdGVtLm9wZW47CiAgICAgICAgfQogICAgICB9CiAgICB9LCBbX2MoJ2RpdicsIFtfdm0uX3YoIlNPUOWGheWuuSIpXSksIF9jKCdkaXYnLCB7CiAgICAgIHN0YXRpY0NsYXNzOiAic3dhcm1zU09QX21lc3NhZ2VfY29udGVudF9ib3hfbGlfcmlnaHQiCiAgICB9LCBbIWl0ZW0ub3BlbiA/IF9jKCdpbWcnLCB7CiAgICAgIGF0dHJzOiB7CiAgICAgICAgInNyYyI6IHJlcXVpcmUoIkAvYXNzZXRzL0FwcGxpY2F0aW9ucy90b3BqMy5wbmciKSwKICAgICAgICAiYWx0IjogIiIKICAgICAgfQogICAgfSkgOiBfdm0uX2UoKSwgaXRlbS5vcGVuID8gX2MoJ2ltZycsIHsKICAgICAgc3RhdGljU3R5bGU6IHsKICAgICAgICAidHJhbnNmb3JtIjogInJvdGF0ZSgxODBkZWcpIgogICAgICB9LAogICAgICBhdHRyczogewogICAgICAgICJzcmMiOiByZXF1aXJlKCJAL2Fzc2V0cy9BcHBsaWNhdGlvbnMvdG9wajMucG5nIiksCiAgICAgICAgImFsdCI6ICIiCiAgICAgIH0KICAgIH0pIDogX3ZtLl9lKCldKV0pLCBpdGVtLm9wZW4gPyBfYygnZGl2JywgewogICAgICBzdGF0aWNDbGFzczogInN3YXJtc1NPUF9jb250ZW50X2xpIgogICAgfSwgW192bS5fbChpdGVtLmxpc3QsIGZ1bmN0aW9uICh1bml0LCBrZXkpIHsKICAgICAgcmV0dXJuIFtfYygnZGl2JywgewogICAgICAgIGtleToga2V5LAogICAgICAgIHN0YXRpY0NsYXNzOiAidW5pdCIKICAgICAgfSwgW19jKCdTaG93U2VuZEluZm8nLCB7CiAgICAgICAga2V5OiBrZXkgKyAxMTEsCiAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICJvYmoiOiB1bml0LndlUXJBdHRhY2htZW50cwogICAgICAgIH0KICAgICAgfSksIF92bS50YWJCYXIgPT09IDAgPyBfYygnZGl2JywgewogICAgICAgIGtleToga2V5LAogICAgICAgIHN0YXRpY0NsYXNzOiAib3BlcmF0aW9uIgogICAgICB9LCBbaXRlbS50eXBlICE9PSAzICYmIHVuaXQuZXhlY3V0ZVN0YXRlID09PSAxID8gX2MoJ3NwYW4nLCB7CiAgICAgICAgc3RhdGljU3R5bGU6IHsKICAgICAgICAgICJjb2xvciI6ICIjMDBiZjJmIgogICAgICAgIH0KICAgICAgfSwgW192bS5fdigi5bey5Y+R6YCBIildKSA6IF92bS5fZSgpLCBpdGVtLnR5cGUgIT09IDMgJiYgdW5pdC5leGVjdXRlU3RhdGUgPT09IDAgPyBfYygnc3BhbicsIHsKICAgICAgICBzdGF0aWNTdHlsZTogewogICAgICAgICAgImNvbG9yIjogIiNlZDQwMTQiCiAgICAgICAgfQogICAgICB9LCBbX3ZtLl92KCLlvoXlj5HpgIEiKV0pIDogX3ZtLl9lKCksIGl0ZW0udHlwZSA9PT0gMyA/IF9jKCdzcGFuJywgW192bS5fdigi5pyq5Yiw5o6o6YCB5pe26Ze0IildKSA6IF92bS5fZSgpLCB1bml0LmV4ZWN1dGVTdGF0ZSA9PT0gMCAmJiBpdGVtLnR5cGUgIT09IDMgPyBfYygndmFuLWJ1dHRvbicsIHsKICAgICAgICBhdHRyczogewogICAgICAgICAgInR5cGUiOiAiaW5mbyIKICAgICAgICB9LAogICAgICAgIG9uOiB7CiAgICAgICAgICAiY2xpY2siOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgICAgIHJldHVybiBfdm0uc2VuZCh1bml0LndlUXJBdHRhY2htZW50cywgdW5pdC5leGVjdXRlVGFyZ2V0QXR0YWNoSWQpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwgW192bS5fdigiIOWPkemAgSAiKV0pIDogX3ZtLl9lKCldLCAxKSA6IF92bS5fZSgpXSwgMSldOwogICAgfSldLCAyKSA6IF92bS5fZSgpXSk7CiAgfSldLCAyKV0pXSwgMSksIF9jKCdMb2FkaW5nJywgewogICAgYXR0cnM6IHsKICAgICAgImlzTG9hZCI6IF92bS5pc0xvYWQKICAgIH0KICB9KSwgX3ZtLnNob3dEZWJ1Z1BhbmVsID8gX2MoJ2RpdicsIHsKICAgIHN0YXRpY0NsYXNzOiAiZGVidWctcGFuZWwiCiAgfSwgW19jKCdkaXYnLCB7CiAgICBzdGF0aWNDbGFzczogImRlYnVnLWhlYWRlciIKICB9LCBbX2MoJ3NwYW4nLCBbX3ZtLl92KCLosIPor5Xkv6Hmga8iKV0pLCBfYygnZGl2JywgewogICAgc3RhdGljQ2xhc3M6ICJkZWJ1Zy1jb250cm9scyIKICB9LCBbX2MoJ2J1dHRvbicsIHsKICAgIHN0YXRpY0NsYXNzOiAiZGVidWctYnRuIiwKICAgIG9uOiB7CiAgICAgICJjbGljayI6IF92bS5kZWJ1Z0RhdGFTdGF0dXMKICAgIH0KICB9LCBbX3ZtLl92KCLosIPor5UiKV0pLCBfYygnYnV0dG9uJywgewogICAgc3RhdGljQ2xhc3M6ICJkZWJ1Zy1idG4iLAogICAgb246IHsKICAgICAgImNsaWNrIjogX3ZtLmNvcHlEZWJ1Z0xvZ3MKICAgIH0KICB9LCBbX3ZtLl92KCLlpI3liLYiKV0pLCBfYygnYnV0dG9uJywgewogICAgc3RhdGljQ2xhc3M6ICJkZWJ1Zy1idG4iLAogICAgYXR0cnM6IHsKICAgICAgInRpdGxlIjogIuWNleWHu+a4hemZpO+8jOWPjOWHu+WujOWFqOa4hemZpCIKICAgIH0sCiAgICBvbjogewogICAgICAiY2xpY2siOiBfdm0uY2xlYXJEZWJ1Z0xvZ3MsCiAgICAgICJkYmxjbGljayI6IF92bS5mb3JjZUNsZWFyRGVidWdMb2dzCiAgICB9CiAgfSwgW192bS5fdigi5riF6ZmkIildKSwgX2MoJ2J1dHRvbicsIHsKICAgIHN0YXRpY0NsYXNzOiAiZGVidWctYnRuIGNsb3NlLWJ0biIsCiAgICBvbjogewogICAgICAiY2xpY2siOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgX3ZtLnNob3dEZWJ1Z1BhbmVsID0gZmFsc2U7CiAgICAgIH0KICAgIH0KICB9LCBbX3ZtLl92KCLlhbPpl60iKV0pXSldKSwgX2MoJ2RpdicsIHsKICAgIHJlZjogImRlYnVnQ29udGVudCIsCiAgICBzdGF0aWNDbGFzczogImRlYnVnLWNvbnRlbnQiCiAgfSwgX3ZtLl9sKF92bS5kZWJ1Z0xvZ3MsIGZ1bmN0aW9uIChsb2csIGluZGV4KSB7CiAgICByZXR1cm4gX2MoJ2RpdicsIHsKICAgICAga2V5OiBpbmRleCwKICAgICAgY2xhc3M6IFsnZGVidWctbG9nJywgbG9nLnR5cGVdCiAgICB9LCBbX2MoJ3NwYW4nLCB7CiAgICAgIHN0YXRpY0NsYXNzOiAiZGVidWctbGV2ZWwiCiAgICB9LCBbX3ZtLl92KF92bS5fcyhsb2cudHlwZS50b1VwcGVyQ2FzZSgpKSldKSwgX2MoJ3NwYW4nLCB7CiAgICAgIHN0YXRpY0NsYXNzOiAiZGVidWctdGltZSIKICAgIH0sIFtfdm0uX3YoX3ZtLl9zKGxvZy50aW1lKSldKSwgX2MoJ3NwYW4nLCB7CiAgICAgIHN0YXRpY0NsYXNzOiAiZGVidWctbWVzc2FnZSIKICAgIH0sIFtfdm0uX3YoX3ZtLl9zKGxvZy5tZXNzYWdlKSldKSwgbG9nLmRhdGEgPyBfYygncHJlJywgewogICAgICBzdGF0aWNDbGFzczogImRlYnVnLWRhdGEiCiAgICB9LCBbX3ZtLl92KF92bS5fcyh0eXBlb2YgbG9nLmRhdGEgPT09ICdvYmplY3QnID8gSlNPTi5zdHJpbmdpZnkobG9nLmRhdGEsIG51bGwsIDIpIDogbG9nLmRhdGEpKV0pIDogX3ZtLl9lKCldKTsKICB9KSwgMCldKSA6IF92bS5fZSgpLCBfYygnZGl2JywgewogICAgc3RhdGljQ2xhc3M6ICJkZWJ1Zy10b2dnbGUiLAogICAgb246IHsKICAgICAgImNsaWNrIjogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgIF92bS5zaG93RGVidWdQYW5lbCA9ICFfdm0uc2hvd0RlYnVnUGFuZWw7CiAgICAgIH0KICAgIH0KICB9LCBbX3ZtLl92KCIg8J+QmyAiKV0pXSwgMSk7Cn07CnZhciBzdGF0aWNSZW5kZXJGbnMgPSBbXTsKZXhwb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfTs="}, null]}