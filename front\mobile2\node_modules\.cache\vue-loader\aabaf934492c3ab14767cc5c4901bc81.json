{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue?vue&type=style&index=0&id=d6bfbcde&prod&lang=less&scoped=true", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue", "mtime": 1753435704303}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751130694554}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751130710731}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751130698336}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1751130697755}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\style-resources-loader\\lib\\index.js", "mtime": 1751130698073}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751130703090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoudW5pdCB7DQogIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogIGJveC1zaGFkb3c6IDBweCAycHggNnB4IDBweCByZ2JhKDYwLCAxMzYsIDI0MCwgMC4xKTsNCiAgYm9yZGVyOiAxcHggc29saWQgI2UxZWRmYzsNCiAgcGFkZGluZzogMTBweDsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KfQ0KLm9wZXJhdGlvbiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIG1hcmdpbi10b3A6IDVweDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KfQ0KLnN3YXJtc1NPUCB7DQogIHdpZHRoOiAxMDAlOw0KICAvLyBoZWlnaHQ6IDEwMHZoOw0KICBiYWNrZ3JvdW5kOiAjZmFmYWZhOw0KICAvLyBwb3NpdGlvbjogcmVsYXRpdmU7DQoNCiAgLnN3YXJtc1NPUF9ib3ggew0KICAgIHBhZGRpbmc6IDEwcHggMTBweCAxMHB4IDEwcHg7DQoNCiAgICAvLyDmi6jmiZPnlLXor51TT1DnmoTlrqLmiLfliJfooajkuI3pnIDopoHlt6blj7NwYWRkaW5nDQogICAgLnBob25lLWNhbGwtY3VzdG9tZXJzIHsNCiAgICAgIG1hcmdpbjogMCAtMTBweDsNCiAgICB9DQogIH0NCg0KICAuc3dhcm1zU09QX21lc3NhZ2Ugew0KICAgIGJvcmRlci1yYWRpdXM6IDNweDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgIG1pbi1oZWlnaHQ6IDI1cHg7DQogICAgcGFkZGluZzogOXB4IDEwcHg7DQogICAgY29sb3I6ICMyMjIxMjE7DQogICAgd2lkdGg6IDEwMCU7DQoNCiAgICAuc3dhcm1zU09QX21lc3NhZ2VfdG9wIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgICAuc3dhcm1zU09QX21lc3NhZ2VfdG9wX2xlZnQgew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KDQogICAgICAgIC5zd2FybXNTT1BfbWVzc2FnZV90b3BfbGVmdF91c2VySW1nIHsNCiAgICAgICAgICB3aWR0aDogNTBweDsNCiAgICAgICAgICBoZWlnaHQ6IDUwcHg7DQogICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgZmxleDogbm9uZTsNCg0KICAgICAgICAgIGltZyB7DQogICAgICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAuc3dhcm1zU09QX21lc3NhZ2VfdG9wX2xlZnRfaW5mbyB7DQogICAgICAgICAgcGFkZGluZy10b3A6IDJweDsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KDQogICAgICAgICAgaW1nIHsNCiAgICAgICAgICAgIHdpZHRoOiAxNnB4Ow0KICAgICAgICAgICAgaGVpZ2h0OiAxNnB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAuc3dhcm1zU09QX21lc3NhZ2VfdG9wX3JpZ2h0IHsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgICAgZ2FwOiA4cHg7DQogICAgICAgIGZsZXg6IG5vbmU7DQoNCiAgICAgICAgLnRyYWNrLXN0YXRlIHsNCiAgICAgICAgICBwYWRkaW5nOiA2cHggMTVweDsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMmM4Y2YwOw0KICAgICAgICAgIGNvbG9yOiAjZmZmOw0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDsNCiAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgIH0NCg0KICAgICAgICAucGhvbmUtYnV0dG9uIHsNCiAgICAgICAgICBwYWRkaW5nOiA2cHggMTVweDsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNjdjMjNhOw0KICAgICAgICAgIGNvbG9yOiAjZmZmOw0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDsNCiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCg0KICAgICAgICAgICY6YWN0aXZlIHsNCiAgICAgICAgICAgIG9wYWNpdHk6IDAuODsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAmLnByaW1hcnkgew0KICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmNmIzNTsNCiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgICAgcGFkZGluZzogOHB4IDIwcHg7DQogICAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgyNTUsIDEwNywgNTMsIDAuMyk7DQogICAgICAgICAgICBhbmltYXRpb246IHB1bHNlIDJzIGluZmluaXRlOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgICYuZGlzYWJsZWQgew0KICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2MwYzRjYzsNCiAgICAgICAgICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7DQogICAgICAgICAgICBhbmltYXRpb246IG5vbmU7DQogICAgICAgICAgICBib3gtc2hhZG93OiBub25lOw0KDQogICAgICAgICAgICAmOmFjdGl2ZSB7DQogICAgICAgICAgICAgIG9wYWNpdHk6IDE7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLnBob25lLWJ1dHRvbi1zbWFsbCB7DQogICAgICAgICAgcGFkZGluZzogNHB4IDhweDsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNDA5ZWZmOw0KICAgICAgICAgIGNvbG9yOiAjZmZmOw0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDNweDsNCiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQoNCiAgICAgICAgICAmOmhvdmVyIHsNCiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICMzMzdlY2M7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgJjphY3RpdmUgew0KICAgICAgICAgICAgb3BhY2l0eTogMC44Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQoNCg0KICAgICAgICBAa2V5ZnJhbWVzIHB1bHNlIHsNCiAgICAgICAgICAwJSB7DQogICAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgyNTUsIDEwNywgNTMsIDAuMyk7DQogICAgICAgICAgfQ0KICAgICAgICAgIDUwJSB7DQogICAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxNnB4IHJnYmEoMjU1LCAxMDcsIDUzLCAwLjYpOw0KICAgICAgICAgIH0NCiAgICAgICAgICAxMDAlIHsNCiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDI1NSwgMTA3LCA1MywgMC4zKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLnBob25lLWNhbGwtdGlwIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmNmIzNSwgI2Y3OTMxZSk7DQogIGNvbG9yOiB3aGl0ZTsNCiAgcGFkZGluZzogNDBweCAyNHB4Ow0KICBib3JkZXItcmFkaXVzOiAyMHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIG1hcmdpbjogMjRweCAxMnB4Ow0KICBib3gtc2hhZG93OiAwIDhweCAyNXB4IHJnYmEoMjU1LCAxMDcsIDUzLCAwLjMpOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIG92ZXJmbG93OiBoaWRkZW47DQoNCiAgJjo6YmVmb3JlIHsNCiAgICBjb250ZW50OiAnJzsNCiAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgdG9wOiAtNTAlOw0KICAgIGxlZnQ6IC01MCU7DQogICAgd2lkdGg6IDIwMCU7DQogICAgaGVpZ2h0OiAyMDAlOw0KICAgIGJhY2tncm91bmQ6IHJhZGlhbC1ncmFkaWVudChjaXJjbGUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAwJSwgdHJhbnNwYXJlbnQgNzAlKTsNCiAgICBhbmltYXRpb246IHNoaW1tZXIgM3MgZWFzZS1pbi1vdXQgaW5maW5pdGU7DQogIH0NCg0KICAudGlwLWljb24gew0KICAgIGZvbnQtc2l6ZTogNjRweDsNCiAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICAgIGFuaW1hdGlvbjogYm91bmNlIDJzIGVhc2UtaW4tb3V0IGluZmluaXRlOw0KICB9DQoNCiAgLnRpcC10ZXh0IHsNCiAgICBmb250LXNpemU6IDIwcHg7DQogICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICAgIHRleHQtc2hhZG93OiAwIDFweCAycHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICB9DQoNCiAgLnRpcC1kZXNjIHsNCiAgICBmb250LXNpemU6IDE1cHg7DQogICAgb3BhY2l0eTogMC45Ow0KICAgIGxpbmUtaGVpZ2h0OiAxLjQ7DQogICAgdGV4dC1zaGFkb3c6IDAgMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogIH0NCn0NCg0KQGtleWZyYW1lcyBzaGltbWVyIHsNCiAgMCUsIDEwMCUgew0KICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKSB0cmFuc2xhdGVZKC01MCUpIHJvdGF0ZSgwZGVnKTsNCiAgfQ0KICA1MCUgew0KICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKSB0cmFuc2xhdGVZKC01MCUpIHJvdGF0ZSgxODBkZWcpOw0KICB9DQp9DQoNCkBrZXlmcmFtZXMgYm91bmNlIHsNCiAgMCUsIDIwJSwgNTAlLCA4MCUsIDEwMCUgew0KICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsNCiAgfQ0KICA0MCUgew0KICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMTBweCk7DQogIH0NCiAgNjAlIHsNCiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTVweCk7DQogIH0NCn0NCg0KQGtleWZyYW1lcyBmYWRlSW5VcCB7DQogIGZyb20gew0KICAgIG9wYWNpdHk6IDA7DQogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDIwcHgpOw0KICB9DQogIHRvIHsNCiAgICBvcGFjaXR5OiAxOw0KICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsNCiAgfQ0KfQ0KDQpAa2V5ZnJhbWVzIHB1bHNlIHsNCiAgMCUgew0KICAgIGJveC1zaGFkb3c6IDAgMCAwIDAgcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjQpOw0KICB9DQogIDcwJSB7DQogICAgYm94LXNoYWRvdzogMCAwIDAgMTBweCByZ2JhKDEwMiwgMTI2LCAyMzQsIDApOw0KICB9DQogIDEwMCUgew0KICAgIGJveC1zaGFkb3c6IDAgMCAwIDAgcmdiYSgxMDIsIDEyNiwgMjM0LCAwKTsNCiAgfQ0KfQ0KDQoucGhvbmUtY2FsbC1jdXN0b21lcnMgew0KICB3aWR0aDogMTAwJTsNCiAgcGFkZGluZzogOHB4IDA7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsICNmN2ZhZmMgMCUsICNlZGYyZjcgMTAwJSk7DQoNCiAgLmN1c3RvbWVyLWl0ZW0gew0KICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZmZmZmYgMCUsICNmOGZhZmMgMTAwJSk7DQogICAgYm9yZGVyLXJhZGl1czogMjBweDsNCiAgICBwYWRkaW5nOiAyMHB4IDE4cHg7DQogICAgbWFyZ2luOiA4cHggMTJweCAxNnB4IDEycHg7DQogICAgYm94LXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMDYpLCAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjA0KTsNCiAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCk7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIHRyYW5zaXRpb246IGFsbCAwLjRzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7DQogICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpOw0KICAgIGFuaW1hdGlvbjogZmFkZUluVXAgMC42cyBlYXNlLW91dDsNCg0KICAgICY6OmJlZm9yZSB7DQogICAgICBjb250ZW50OiAnJzsNCiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgIHRvcDogMDsNCiAgICAgIGxlZnQ6IDA7DQogICAgICB3aWR0aDogNXB4Ow0KICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDIwcHggMCAwIDIwcHg7DQogICAgfQ0KDQogICAgJjo6YWZ0ZXIgew0KICAgICAgY29udGVudDogJyc7DQogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICB0b3A6IC01MCU7DQogICAgICBsZWZ0OiAtNTAlOw0KICAgICAgd2lkdGg6IDIwMCU7DQogICAgICBoZWlnaHQ6IDIwMCU7DQogICAgICBiYWNrZ3JvdW5kOiByYWRpYWwtZ3JhZGllbnQoY2lyY2xlLCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMDMpIDAlLCB0cmFuc3BhcmVudCA3MCUpOw0KICAgICAgb3BhY2l0eTogMDsNCiAgICAgIHRyYW5zaXRpb246IG9wYWNpdHkgMC4zcyBlYXNlOw0KICAgIH0NCg0KICAgICY6aG92ZXIgew0KICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0zcHgpIHNjYWxlKDEuMDEpOw0KICAgICAgYm94LXNoYWRvdzogMCAxMnB4IDQwcHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjE1KSwgMCA0cHggMTZweCByZ2JhKDAsIDAsIDAsIDAuMDgpOw0KICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMik7DQoNCiAgICAgICY6OmFmdGVyIHsNCiAgICAgICAgb3BhY2l0eTogMTsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAuY3VzdG9tZXItaW5mbyB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIGZsZXg6IDE7DQogICAgICBtaW4td2lkdGg6IDA7IC8vIOmYsuatomZsZXjlrZDlhYPntKDmuqLlh7oNCg0KICAgICAgLmN1c3RvbWVyLWF2YXRhciB7DQogICAgICAgIHdpZHRoOiA1NnB4Ow0KICAgICAgICBoZWlnaHQ6IDU2cHg7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxNnB4Ow0KICAgICAgICBmbGV4LXNocmluazogMDsNCiAgICAgICAgYm9yZGVyOiAzcHggc29saWQgcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjE1KTsNCiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTZweCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMSk7DQogICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCg0KICAgICAgICAmOmhvdmVyIHsNCiAgICAgICAgICBhbmltYXRpb246IHB1bHNlIDJzIGluZmluaXRlOw0KICAgICAgICB9DQoNCiAgICAgICAgaW1nIHsNCiAgICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICAgICAgb2JqZWN0LWZpdDogY292ZXI7DQogICAgICAgIH0NCg0KICAgICAgICAuZGVmYXVsdC1hdmF0YXIgew0KICAgICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOw0KICAgICAgICAgIGNvbG9yOiB3aGl0ZTsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICAgICAgZm9udC1zaXplOiAyMnB4Ow0KICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICAgICAgdGV4dC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMik7DQogICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KDQogICAgICAgICAgJjo6YmVmb3JlIHsNCiAgICAgICAgICAgIGNvbnRlbnQ6ICcnOw0KICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICAgICAgdG9wOiAwOw0KICAgICAgICAgICAgbGVmdDogMDsNCiAgICAgICAgICAgIHJpZ2h0OiAwOw0KICAgICAgICAgICAgYm90dG9tOiAwOw0KICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpIDAlLCB0cmFuc3BhcmVudCA1MCUpOw0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAuY3VzdG9tZXItZGV0YWlscyB7DQogICAgICAgIGZsZXg6IDE7DQogICAgICAgIG1pbi13aWR0aDogMDsgLy8g6Ziy5q2i5paH5pys5rqi5Ye6DQogICAgICAgIHBhZGRpbmc6IDJweCAwOw0KDQogICAgICAgIC5jdXN0b21lci1uYW1lIHsNCiAgICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDsNCiAgICAgICAgICBjb2xvcjogIzJkMzc0ODsNCiAgICAgICAgICBtYXJnaW4tYm90dG9tOiA2cHg7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMjsNCiAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IC0wLjJweDsNCiAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgICAgICB9DQoNCiAgICAgICAgLmN1c3RvbWVyLXBob25lIHsNCiAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgY29sb3I6ICM0YTU1Njg7DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICAgICAgICAgIGZvbnQtZmFtaWx5OiAnU0YgTW9ubycsICdNb25hY28nLCAnSW5jb25zb2xhdGEnLCAnUm9ib3RvIE1vbm8nLCBtb25vc3BhY2U7DQogICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuNXB4Ow0KICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4wNSk7DQogICAgICAgICAgcGFkZGluZzogMnB4IDhweDsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgICAgICB9DQoNCiAgICAgICAgLmN1c3RvbWVyLXN0YXR1cyB7DQogICAgICAgICAgLnN0YXR1cy1wZW5kaW5nIHsNCiAgICAgICAgICAgIGNvbG9yOiAjZTUzZTNlOw0KICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjI5LCA2MiwgNjIsIDAuMSkgMCUsIHJnYmEoMjI5LCA2MiwgNjIsIDAuMDUpIDEwMCUpOw0KICAgICAgICAgICAgcGFkZGluZzogNHB4IDEycHg7DQogICAgICAgICAgICBib3JkZXItcmFkaXVzOiAxNnB4Ow0KICAgICAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyMjksIDYyLCA2MiwgMC4yKTsNCiAgICAgICAgICAgIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7DQogICAgICAgICAgICBsZXR0ZXItc3BhY2luZzogMC41cHg7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLnN0YXR1cy1jb21wbGV0ZWQgew0KICAgICAgICAgICAgY29sb3I6ICMzOGExNjk7DQogICAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSg1NiwgMTYxLCAxMDUsIDAuMSkgMCUsIHJnYmEoNTYsIDE2MSwgMTA1LCAwLjA1KSAxMDAlKTsNCiAgICAgICAgICAgIHBhZGRpbmc6IDRweCAxMnB4Ow0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMTZweDsNCiAgICAgICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoNTYsIDE2MSwgMTA1LCAwLjIpOw0KICAgICAgICAgICAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTsNCiAgICAgICAgICAgIGxldHRlci1zcGFjaW5nOiAwLjVweDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAuY2FsbC10aW1lIHsNCiAgICAgICAgICBmb250LXNpemU6IDEzcHg7DQogICAgICAgICAgY29sb3I6ICM2NjdlZWE7DQogICAgICAgICAgbWFyZ2luLXRvcDogOHB4Ow0KICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4wOCkgMCUsIHJnYmEoMTE4LCA3NSwgMTYyLCAwLjA1KSAxMDAlKTsNCiAgICAgICAgICBwYWRkaW5nOiA2cHggMTJweDsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICAgICAgICAgIGRpc3BsYXk6IGlubGluZS1mbGV4Ow0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjE1KTsNCiAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KDQogICAgICAgICAgLmNhbGwtdGltZS1pY29uIHsNCiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogNnB4Ow0KICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgICAgb3BhY2l0eTogMC44Ow0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5jYWxsLXRpbWUtdGV4dCB7DQogICAgICAgICAgICBmb250LWZhbWlseTogJ1NGIE1vbm8nLCAnTW9uYWNvJywgJ0luY29uc29sYXRhJywgJ1JvYm90byBNb25vJywgbW9ub3NwYWNlOw0KICAgICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuM3B4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5jdXN0b21lci1hY3Rpb25zIHsNCiAgICAgIG1hcmdpbi1sZWZ0OiAxMnB4Ow0KICAgICAgZmxleC1zaHJpbms6IDA7DQoNCiAgICAgIC5hY3Rpb24tYnV0dG9ucyB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgIGdhcDogOHB4Ow0KICAgICAgfQ0KDQogICAgICAucGhvbmUtY2FsbC1idG4sIC52b2ljZS1jYWxsLWJ0biB7DQogICAgICAgIHBhZGRpbmc6IDE0cHggMjRweDsNCiAgICAgICAgY29sb3I6IHdoaXRlOw0KICAgICAgICBib3JkZXItcmFkaXVzOiAzMHB4Ow0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7DQogICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuNHMgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTsNCiAgICAgICAgYm9yZGVyOiBub25lOw0KICAgICAgICBvdXRsaW5lOiBub25lOw0KICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgbWluLXdpZHRoOiAxMTBweDsNCiAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuM3B4Ow0KICAgICAgICBib3gtc2hhZG93OiAwIDZweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4xMik7DQoNCiAgICAgICAgJjo6YmVmb3JlIHsNCiAgICAgICAgICBjb250ZW50OiAnJzsNCiAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICAgICAgdG9wOiAwOw0KICAgICAgICAgIGxlZnQ6IC0xMDAlOw0KICAgICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHRyYW5zcGFyZW50LCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMiksIHRyYW5zcGFyZW50KTsNCiAgICAgICAgICB0cmFuc2l0aW9uOiBsZWZ0IDAuNXM7DQogICAgICAgIH0NCg0KICAgICAgICAmOmFjdGl2ZSB7DQogICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC5waG9uZS1jYWxsLWJ0biB7DQogICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogICAgICAgIGJveC1zaGFkb3c6IDAgNnB4IDIwcHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjMpOw0KDQogICAgICAgICY6aG92ZXIgew0KICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtM3B4KSBzY2FsZSgxLjAyKTsNCiAgICAgICAgICBib3gtc2hhZG93OiAwIDhweCAzMHB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC40KTsNCg0KICAgICAgICAgICY6OmJlZm9yZSB7DQogICAgICAgICAgICBsZWZ0OiAxMDAlOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgICY6YWN0aXZlIHsNCiAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCkgc2NhbGUoMS4wMSk7DQogICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMyk7DQogICAgICAgIH0NCg0KICAgICAgICAmLmRpc2FibGVkIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjYmRjM2M3LCAjOTVhNWE2KTsNCiAgICAgICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkOw0KICAgICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQoNCiAgICAgICAgICAmOmhvdmVyIHsNCiAgICAgICAgICAgIHRyYW5zZm9ybTogbm9uZTsNCiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQoNCiAgICAgICAgICAgICY6OmJlZm9yZSB7DQogICAgICAgICAgICAgIGxlZnQ6IC0xMDAlOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAudm9pY2UtY2FsbC1idG4gew0KICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNDhiYjc4IDAlLCAjMzhhMTY5IDEwMCUpOw0KICAgICAgICBib3gtc2hhZG93OiAwIDZweCAyMHB4IHJnYmEoNzIsIDE4NywgMTIwLCAwLjMpOw0KDQogICAgICAgICY6aG92ZXIgew0KICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtM3B4KSBzY2FsZSgxLjAyKTsNCiAgICAgICAgICBib3gtc2hhZG93OiAwIDhweCAzMHB4IHJnYmEoNzIsIDE4NywgMTIwLCAwLjQpOw0KDQogICAgICAgICAgJjo6YmVmb3JlIHsNCiAgICAgICAgICAgIGxlZnQ6IDEwMCU7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgJjphY3RpdmUgew0KICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KSBzY2FsZSgxLjAxKTsNCiAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxNXB4IHJnYmEoNzIsIDE4NywgMTIwLCAwLjMpOw0KICAgICAgICB9DQoNCiAgICAgICAgJi5kaXNhYmxlZCB7DQogICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2JkYzNjNywgIzk1YTVhNik7DQogICAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDsNCiAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KDQogICAgICAgICAgJjpob3ZlciB7DQogICAgICAgICAgICB0cmFuc2Zvcm06IG5vbmU7DQogICAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KDQogICAgICAgICAgICAmOjpiZWZvcmUgew0KICAgICAgICAgICAgICBsZWZ0OiAtMTAwJTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAvLyDlk43lupTlvI/orr7orqENCiAgQG1lZGlhIChtYXgtd2lkdGg6IDM3NXB4KSB7DQogICAgLmN1c3RvbWVyLWl0ZW0gew0KICAgICAgbWFyZ2luOiA2cHggOHB4IDEycHggOHB4Ow0KICAgICAgcGFkZGluZzogMTZweCAxNHB4Ow0KDQogICAgICAuY3VzdG9tZXItaW5mbyB7DQogICAgICAgIC5jdXN0b21lci1hdmF0YXIgew0KICAgICAgICAgIHdpZHRoOiA0OHB4Ow0KICAgICAgICAgIGhlaWdodDogNDhweDsNCiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDE0cHg7DQogICAgICAgIH0NCg0KICAgICAgICAuY3VzdG9tZXItZGV0YWlscyB7DQogICAgICAgICAgLmN1c3RvbWVyLW5hbWUgew0KICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5jdXN0b21lci1waG9uZSB7DQogICAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC5hY3Rpb24tYnV0dG9ucyB7DQogICAgICAgIC5waG9uZS1jYWxsLWJ0biwgLnZvaWNlLWNhbGwtYnRuIHsNCiAgICAgICAgICBwYWRkaW5nOiAxMnB4IDE4cHg7DQogICAgICAgICAgZm9udC1zaXplOiAxM3B4Ow0KICAgICAgICAgIG1pbi13aWR0aDogOTBweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQouc3dhcm1zU09QX3RhYkJhciB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGJvcmRlci1yYWRpdXM6IDEwcHggMTBweCAwIDA7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIC8vIGJvcmRlcjogMXB4IHNvbGlkICM3ZjdmN2Y7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIC5zd2FybXNTT1BfdGFiQmFyX2xpIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICAgIGNvbG9yOiAjN2Y3ZjdmOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBmbGV4OiAxOw0KICAgIGhlaWdodDogNDBweDsNCiAgfQ0KICAuc3dhcm1zU09QX3RhYkJhcl9saTEgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICM3ZjdmN2Y7DQogICAgY29sb3I6ICNmZmY7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGZsZXg6IDE7DQogICAgaGVpZ2h0OiA0MHB4Ow0KICB9DQp9DQouc3dhcm1zU09QX2NvbnRlbnQgew0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBib3JkZXItcmFkaXVzOiAxMHB4Ow0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KICB3aWR0aDogMTAwJTsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgLnN3YXJtc1NPUF9jb250ZW50X3RvcDEgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICM5MGY4ZTM7DQogICAgcGFkZGluZzogMTVweCAxMHB4Ow0KICAgIC5zd2FybXNTT1BfY29udGVudF90b3BfdGV4dCB7DQogICAgICBjb2xvcjogIzFhYmM5YzsNCiAgICB9DQogIH0NCiAgLnN3YXJtc1NPUF9jb250ZW50X3RvcDIgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmZGIyYTA7DQogICAgcGFkZGluZzogMTVweCAxMHB4Ow0KICAgIC5zd2FybXNTT1BfY29udGVudF90b3BfdGV4dCB7DQogICAgICBjb2xvcjogI2VkNDAxNDsNCiAgICB9DQogIH0NCiAgLnN3YXJtc1NPUF9jb250ZW50X3RvcDMgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNiNWIxYjE7DQogICAgcGFkZGluZzogMTVweCAxMHB4Ow0KICAgIC5zd2FybXNTT1BfY29udGVudF90b3BfdGV4dCB7DQogICAgICBjb2xvcjogIzdmN2Y3ZjsNCiAgICB9DQogIH0NCn0NCi5zd2FybXNTT1BfY29udGVudF90aXRsZSB7DQogIHBhZGRpbmc6IDEwcHggMTBweDsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KfQ0KLnN3YXJtc1NPUF9tZXNzYWdlX2NvbnRlbnRfYm94X2xpX3JpZ2h0IHsNCiAgd2lkdGg6IDE2cHg7DQogIGhlaWdodDogMTZweDsNCiAgaW1nIHsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBoZWlnaHQ6IDEwMCU7DQogIH0NCn0NCi5zd2FybXNTT1BfY29udGVudF9saSB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNmZhZmY7DQogIHBhZGRpbmc6IDEwcHg7DQogIC5zd2FybXNTT1BfY29udGVudF9saV90ZXh0IHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICB9DQp9DQoNCi8qIOiwg+ivlemdouadv+agt+W8jyAqLw0KLmRlYnVnLXBhbmVsIHsNCiAgcG9zaXRpb246IGZpeGVkOw0KICB0b3A6IDEwcHg7DQogIGxlZnQ6IDEwcHg7DQogIHJpZ2h0OiAxMHB4Ow0KICBib3R0b206IDEwcHg7DQogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC45NSk7DQogIGNvbG9yOiAjZmZmOw0KICB6LWluZGV4OiA5OTk5Ow0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGZvbnQtZmFtaWx5OiAnQ291cmllciBOZXcnLCBtb25vc3BhY2U7DQp9DQoNCi5kZWJ1Zy1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBhZGRpbmc6IDEwcHggMTVweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICMzMzM7DQogIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTsNCiAgYm9yZGVyLXJhZGl1czogOHB4IDhweCAwIDA7DQp9DQoNCi5kZWJ1Zy1jb250cm9scyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogOHB4Ow0KfQ0KDQouZGVidWctYnRuIHsNCiAgcGFkZGluZzogNHB4IDhweDsNCiAgYmFja2dyb3VuZDogIzAwN2FmZjsNCiAgY29sb3I6IHdoaXRlOw0KICBib3JkZXI6IG5vbmU7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQp9DQoNCi5kZWJ1Zy1idG46aG92ZXIgew0KICBiYWNrZ3JvdW5kOiAjMDA1NmIzOw0KfQ0KDQouZGVidWctYnRuLmNsb3NlLWJ0biB7DQogIGJhY2tncm91bmQ6ICNmZjNiMzA7DQp9DQoNCi5kZWJ1Zy1idG4uY2xvc2UtYnRuOmhvdmVyIHsNCiAgYmFja2dyb3VuZDogI2Q3MDAxNTsNCn0NCg0KLmRlYnVnLWNvbnRlbnQgew0KICBmbGV4OiAxOw0KICBvdmVyZmxvdy15OiBhdXRvOw0KICBwYWRkaW5nOiAxMHB4Ow0KICBmb250LXNpemU6IDEycHg7DQogIGxpbmUtaGVpZ2h0OiAxLjQ7DQp9DQoNCi5kZWJ1Zy1sb2cgew0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQogIHBhZGRpbmc6IDZweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBib3JkZXItbGVmdDogM3B4IHNvbGlkICMwMDdhZmY7DQp9DQoNCi5kZWJ1Zy1sb2cuaW5mbyB7DQogIGJhY2tncm91bmQ6IHJnYmEoMCwgMTIyLCAyNTUsIDAuMSk7DQogIGJvcmRlci1sZWZ0LWNvbG9yOiAjMDA3YWZmOw0KfQ0KDQouZGVidWctbG9nLndhcm4gew0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMTQ5LCAwLCAwLjEpOw0KICBib3JkZXItbGVmdC1jb2xvcjogI2ZmOTUwMDsNCn0NCg0KLmRlYnVnLWxvZy5lcnJvciB7DQogIGJhY2tncm91bmQ6IHJnYmEoMjU1LCA1OSwgNDgsIDAuMSk7DQogIGJvcmRlci1sZWZ0LWNvbG9yOiAjZmYzYjMwOw0KfQ0KDQouZGVidWctbG9nLnN1Y2Nlc3Mgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDUyLCAxOTksIDg5LCAwLjEpOw0KICBib3JkZXItbGVmdC1jb2xvcjogIzM0Yzc1OTsNCn0NCg0KLmRlYnVnLWxvZy5hcGkgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDg4LCA4NiwgMjE0LCAwLjEpOw0KICBib3JkZXItbGVmdC1jb2xvcjogIzU4NTZkNjsNCn0NCg0KLmRlYnVnLWxldmVsIHsNCiAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICBwYWRkaW5nOiAycHggNnB4Ow0KICBib3JkZXItcmFkaXVzOiAzcHg7DQogIGZvbnQtc2l6ZTogMTBweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIG1hcmdpbi1yaWdodDogOHB4Ow0KICBtaW4td2lkdGg6IDQwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLmRlYnVnLWxvZy5pbmZvIC5kZWJ1Zy1sZXZlbCB7DQogIGJhY2tncm91bmQ6ICMwMDdhZmY7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLmRlYnVnLWxvZy53YXJuIC5kZWJ1Zy1sZXZlbCB7DQogIGJhY2tncm91bmQ6ICNmZjk1MDA7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLmRlYnVnLWxvZy5lcnJvciAuZGVidWctbGV2ZWwgew0KICBiYWNrZ3JvdW5kOiAjZmYzYjMwOw0KICBjb2xvcjogd2hpdGU7DQp9DQoNCi5kZWJ1Zy1sb2cuc3VjY2VzcyAuZGVidWctbGV2ZWwgew0KICBiYWNrZ3JvdW5kOiAjMzRjNzU5Ow0KICBjb2xvcjogd2hpdGU7DQp9DQoNCi5kZWJ1Zy1sb2cuYXBpIC5kZWJ1Zy1sZXZlbCB7DQogIGJhY2tncm91bmQ6ICM1ODU2ZDY7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLmRlYnVnLXRpbWUgew0KICBjb2xvcjogIzg4ODsNCiAgZm9udC1zaXplOiAxMHB4Ow0KICBtYXJnaW4tcmlnaHQ6IDhweDsNCn0NCg0KLmRlYnVnLW1lc3NhZ2Ugew0KICBjb2xvcjogI2ZmZjsNCn0NCg0KLmRlYnVnLWRhdGEgew0KICBtYXJnaW4tdG9wOiA0cHg7DQogIHBhZGRpbmc6IDhweDsNCiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBmb250LXNpemU6IDEwcHg7DQogIHdoaXRlLXNwYWNlOiBwcmUtd3JhcDsNCiAgd29yZC1icmVhazogYnJlYWstYWxsOw0KICBjb2xvcjogI2NjYzsNCiAgbWF4LWhlaWdodDogMjAwcHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQoNCi5kZWJ1Zy10b2dnbGUgew0KICBwb3NpdGlvbjogZml4ZWQ7DQogIGJvdHRvbTogMjBweDsNCiAgcmlnaHQ6IDIwcHg7DQogIHdpZHRoOiA1MHB4Ow0KICBoZWlnaHQ6IDUwcHg7DQogIGJhY2tncm91bmQ6ICMwMDdhZmY7DQogIGNvbG9yOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogNTAlOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgZm9udC1zaXplOiAyMHB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHotaW5kZXg6IDEwMDA7DQogIGJveC1zaGFkb3c6IDAgMnB4IDEwcHggcmdiYSgwLCAwLCAwLCAwLjMpOw0KfQ0KDQouZGVidWctdG9nZ2xlOmhvdmVyIHsNCiAgYmFja2dyb3VuZDogIzAwNTZiMzsNCn0NCg0K"}, null]}