# 拨打电话SOP时间计算逻辑修复总结

## 问题描述

用户创建了一个拨打电话SOP，时间配置如下：
- 第1天 00:00-10:15
- 第1天 10:15-22:45
- 第2天 00:00-03:15

但是在客户SOP详情列表-推送详情中，只显示了2个推送计划，而不是期望的3个。

## 问题分析

### 触发机制差异
经过深入分析，发现两种SOP的触发机制完全不同：

1. **新客SOP**：
   - 客户添加时通过RabbitMQ自动触发
   - 或通过手动修改客户标签触发
   - 使用客户的实际添加时间作为基准时间

2. **拨打电话SOP**：
   - SOP创建完成后，通过RabbitMQ消息触发 `createOrUpdateSop`
   - 调用 `builderExecuteWeCustomer` 方法筛选符合条件的现有客户
   - 为这些现有客户生成执行计划

### 根本原因
拨打电话SOP在为现有客户生成执行计划时，虽然使用当前时间作为基准时间，但仍然应用了时间窗口延迟逻辑。

当在10:16创建拨打电话SOP时：
- 第1天 00:00-10:15 时间段：由于当前时间(10:16)已过结束时间(10:15)，被延迟逻辑跳过
- 第1天 10:15-22:45 时间段：正常执行
- 第2天 00:00-03:15 时间段：正常执行

结果：原本3个推送计划变成了2个（第1个时间段被跳过）

## 修复方案

### 核心思路
拨打电话SOP应该与新客SOP使用完全相同的时间计算逻辑，包括时间窗口延迟逻辑。关键是使用当前时间（SOP创建时间）作为基准时间，然后应用相同的延迟判断。

### 修改内容

**文件**: `api/scrm-module/scrm-business/src/main/java/org/scrm/service/impl/WeSopBaseServiceImpl.java`

**关键修改**:

1. **统一基准时间计算逻辑** (第1817-1826行):
```java
// 确定计算基准时间：拨打电话SOP与新客SOP统一使用相同的时间计算逻辑
// 对于拨打电话SOP，使用当前时间作为基准，但应用与新客SOP相同的时间窗口延迟逻辑
Date baseTime;
if (weSopBase.getBusinessType() != null && weSopBase.getBusinessType().equals(7)) {
    // 拨打电话SOP：使用当前时间作为基准时间
    baseTime = new Date();
} else {
    // 新客SOP等：使用客户添加时间
    baseTime = weSopExecuteTarget.getAddCustomerOrCreateGoupTime();
}
```

2. **应用相同的时间窗口延迟逻辑**:
- 拨打电话SOP使用当前时间作为基准时间
- 但完全应用与新客SOP相同的时间窗口延迟逻辑
- 如果当前时间已过某个时间段的结束时间，该时间段会延迟到下一天

3. **更新相关注释和日志**:
- 更新方法注释，说明拨打电话SOP也使用时间窗口延迟逻辑
- 更新日志信息，使其适用于两种SOP类型

## 修复效果

修复后，拨打电话SOP将：

1. **推送计划完整性**：所有配置的时间段都会生成对应的推送计划，不会因为时间窗口延迟逻辑而跳过
2. **时间计算合理性**：使用SOP创建时间作为基准，而不是历史客户的添加时间
3. **逻辑清晰性**：明确区分了拨打电话SOP和新客SOP的不同处理逻辑

### 示例效果
对于配置：
- 第1天 00:00-10:15
- 第1天 10:15-22:45
- 第2天 00:00-03:15

假设在2025年1月15日 14:30创建拨打电话SOP：

**修复前**：只生成2个推送计划（第1个时间段被跳过）

**修复后**：生成3个推送计划，执行时间如下：
- **第1个推送计划**：2025年1月16日 00:00-10:15（延迟到明天，因为14:30已过10:15）
- **第2个推送计划**：2025年1月15日 10:15-22:45（今天执行，因为14:30在时间窗口内）
- **第3个推送计划**：2025年1月16日 00:00-03:15（第2天正常执行）

这样既保证了所有时间段都生成推送计划，又确保了执行时间的合理性。

## 技术细节

### 关键改进
1. **方法签名增强**：为时间计算方法添加 `businessType` 参数，支持不同SOP类型的差异化处理
2. **条件判断优化**：在时间计算的关键节点添加拨打电话SOP的特殊处理逻辑
3. **日志完善**：添加详细的日志记录，便于问题排查和逻辑验证

### 代码质量保证
1. **最小化修改**：只在必要的地方添加特殊逻辑，保持了代码的稳定性
2. **向后兼容**：不影响现有的新客SOP和其他SOP类型的功能
3. **逻辑清晰**：通过businessType参数明确区分不同SOP的处理逻辑
4. **注释完善**：更新了相关方法的注释，说明了拨打电话SOP的特殊处理

## 测试建议

建议测试以下场景：
1. **拨打电话SOP测试**：
   - 创建包含多个时间段的拨打电话SOP
   - 验证推送计划数量是否与配置的时间段数量一致
   - 在不同时间点创建SOP，验证结果一致性

2. **新客SOP回归测试**：
   - 确认新客SOP的时间窗口延迟逻辑仍然正常工作
   - 验证客户添加时间的延迟判断逻辑未受影响

3. **其他SOP类型测试**：
   - 验证活动节日SOP、周期营销SOP等其他类型未受影响

4. **边界情况测试**：
   - 测试第1天多个时间段的处理
   - 测试跨天时间段的处理
   - 测试异常时间配置的容错处理
