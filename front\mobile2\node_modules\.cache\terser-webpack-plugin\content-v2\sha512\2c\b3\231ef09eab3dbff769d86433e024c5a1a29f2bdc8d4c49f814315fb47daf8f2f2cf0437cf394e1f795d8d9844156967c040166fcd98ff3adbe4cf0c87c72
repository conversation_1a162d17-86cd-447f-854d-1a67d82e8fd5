{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-ec6120b8\"],{5584:function(t,e,s){},\"6bc2\":function(t,e,s){\"use strict\";s(\"952e\")},\"6fc5\":function(t,e,s){},\"76f0\":function(t,e,s){},\"952e\":function(t,e,s){},a1f1:function(t,e,s){t.exports=s.p+\"img/default_icon.fa7bdcbd.png\"},a6a3:function(t,e){t.exports=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAutJREFUOE+1lE1oHWUUhp/zzcz9Te5NML0hFQS5GEgqheBfi8nGTVBIQLQuYtUqiOK2qyqFQqG6Ehd1oVDRjaCt4l/3ohZ/SqORkhBtU2kx2mprNMncv5lz5LvT2LTrOgMzfMxwvve873M+MTPjJlzyPxQyTJvEF47iSAC5TqfgUFKCvnFylTsAQ8T99881RWasL78D517FcLgbGvZLZ46kMkJ5+1uIhIgE1wqp98gMTS4TfzNFQIcU4fiJBheXlZHRIvNnYnZNVqmUGoiGUN9PeWj6BkWqpijNxQOkf36GM+HjL9pc+MMRhSljw3m+W0iJRNn9UIneXIckqlK65yOCsLpJkZp1Vk/R/vE5rqwYuw9cotVQTBJ6ooRHJmu8ffxvBMURsPfJPh7ckcdtnaFY39stJP5OtG2Nueex9ZN8P6/MzseoeKOVXAB3DpeYXWgipJgIPZHj8akKqo7C2LuE5TpIgDSXP7T0zEEU4f4nllhPy5j4ldAbNZmZHuKNYysgmiVpypH9A9w16pDqTorbDndNl79O7rJc8yfUAk6draJqCD6jLPBSCdZirzCLWjHqtTW2VDukFlC89ygufzsSX/zU0sUXEfLsfGaJZquAbmTvoe/ilDHlnyLCkZcGuXvY0FsmKI6+1t1CUk0snnsBWf2WHxZbfDXfJrDU40ZggkkGlDMjdUZfIc/MZD9JaBTH3ico3uap82ardVZPk849zT8NYd/rMa0kayx1itgGvcJA/yD7xguUg8uEO/ZQ3vZsV2FXral6HIl/fhm79AGSOo592WHxF2+u/+JthyhMeOqBUQZunaB3+8N0Pj9EfurN60ekC7deYe3rRwlthUQCTsy2OX0uJdGASn6VxyZrFHJbcQvnifrrJEP3URrPOMoUmR+SLKXmb++RLL1CaBsz5DPyqq5C59/hBK3fW/RMHyYMok2FfGdXDTVtE//6CdLlqNtZFpWBid8V1oIRUlejNrgFt3lob9Z59C+g21rNwG+hqQAAAABJRU5ErkJggg==\"},bab2:function(t,e,s){\"use strict\";s(\"5584\")},d30f:function(t,e,s){\"use strict\";s(\"76f0\")},d800:function(t,e,s){\"use strict\";s.d(e,\"h\",(function(){return n})),s.d(e,\"f\",(function(){return c})),s.d(e,\"c\",(function(){return l})),s.d(e,\"d\",(function(){return d})),s.d(e,\"b\",(function(){return u})),s.d(e,\"g\",(function(){return h})),s.d(e,\"i\",(function(){return g})),s.d(e,\"e\",(function(){return m})),s.d(e,\"a\",(function(){return p}));var a=s(\"b775\");const i=window.sysConfig.services.wecom,o=i+\"/material\",r=window.sysConfig.services.weChat;function n(){return Object(a[\"a\"])({url:o+\"/media/type\"})}function c(t){return Object(a[\"a\"])({url:o+\"/temporaryMaterialMediaId\",params:t})}function l(t){return Object(a[\"a\"])({url:i+\"/category/list\",params:t})}function d(t){return Object(a[\"a\"])({url:o+\"/list\",params:t})}function u(t){return Object(a[\"a\"])({url:i+\"/material/action/addSend\",method:\"post\",data:t})}function h(t){return Object(a[\"a\"])({url:r+\"/track/material/auth/get\",params:t})}function g(t){return Object(a[\"a\"])({url:i+\"/talk/list\",params:t})}function m(t){return Object(a[\"a\"])({url:r+\"/material/get/\"+t})}function p(t){return Object(a[\"a\"])({url:i+\"/material/action/addAllSend\",method:\"post\",data:t})}},e0de:function(t,e){t.exports=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAZRJREFUOE99ks9LVFEYhp9zzr13o4kLBQu0XUm6lJAWYbhPW8joZJKbcCMigtB/IAhClP+AlI4DIhIRorg0IaI2tXKRYNAk1MJBYe4935Ez4/wInTmbs/jOc17e732Vc87R4NROlao+VPVAD/g/c38c+byjrU3T2qoos9eCJQgy2ZjPXxLCUCMiPBmKeNBv8Mp1wf1PMe8/WKZehHR1Gr5+S8huWGanQzo69FWw7Gl1PcFaxfhYScFax8JigeHHhp57QRX0fkQUIg4TKFYzUtzE01FNoeDYP4jZ3hHmZiLa2y8VvcrPIyGTTbh1E56NRxUwndKsvIs5PHSkRjS9PUHV499/wtKrmPt98Gggoqmpqpge1ZyfOYIQolAVIX+UiHPffyRsbllezkcYU5q8XbNoDemUqYR3JcfjX5bXywmTzwO67xh+54Q3ywmDg4qBh2FFpbYnxTj8Ynb3LB+3Y1puGE7zQvddzeRESBDU1KWGrOToe5fLCScnQnOz4nbXZdDXc//n6LfrfZTvRh2u29VGkJ9dAMQdydX+xEqUAAAAAElFTkSuQmCC\"},e29b:function(t,e,s){\"use strict\";s.r(e);var a=function(){var t=this,e=t._self._c;return e(\"div\",[\"customer\"===t.currentType?[e(\"customer\",{attrs:{userId:t.externalUserId,sopType:t.sopType,sopBaseId:t.sopBaseId,executeTargetAttachId:t.executeTargetAttachId}})]:[e(\"group\",{attrs:{chatId:t.chatId,sopType:t.sopType}})]],2)},i=[],o=function(){var t=this,e=t._self._c;return e(\"div\",{staticClass:\"swarmsSOP\"},[e(\"div\",[e(\"van-sticky\",[e(\"div\",{staticStyle:{background:\"#fafafa\"}},[t.isPhoneCallSop?t._e():e(\"div\",{staticClass:\"swarmsSOP_message\"},[e(\"div\",{staticClass:\"swarmsSOP_message_top\"},[e(\"div\",{staticClass:\"swarmsSOP_message_top_left\"},[e(\"div\",{staticClass:\"swarmsSOP_message_top_left_userImg\"},[t.form.avatar?e(\"img\",{attrs:{src:t.form.avatar,alt:\"\"}}):t._e()]),e(\"div\",{staticClass:\"swarmsSOP_message_top_left_info\"},[e(\"div\",[t._v(\" \"+t._s(t.form.customerName)+\" \"),e(\"span\",{style:{color:1===t.form.customerType?\"#4bde03\":\"#f9a90b\"}},[t._v(\" \"+t._s({1:\"@微信\",2:t.form.corpName?\"@\"+t.form.corpName:\"@企业微信\"}[t.form.customerType])+\" \")])]),e(\"div\",[1===t.form.gender?e(\"img\",{attrs:{src:s(\"d378\")}}):2===t.form.gender?e(\"img\",{attrs:{src:s(\"2982\")}}):t._e()])])]),e(\"div\",{staticClass:\"swarmsSOP_message_top_right\"},[e(\"div\",{staticClass:\"track-state\"},[t._v(t._s(t.trackState))])])])]),e(\"div\",{staticClass:\"swarmsSOP_tabBar\"},[e(\"div\",{class:0==t.tabBar?\"swarmsSOP_tabBar_li1\":\"swarmsSOP_tabBar_li\",on:{click:function(e){return t.setChange(0)}}},[t._v(\" \"+t._s(t.isPhoneCallSop?\"待拨打\":\"待推送\")+\" \")]),e(\"div\",{class:1==t.tabBar?\"swarmsSOP_tabBar_li1\":\"swarmsSOP_tabBar_li\",on:{click:function(e){return t.setChange(1)}}},[t._v(\" \"+t._s(t.isPhoneCallSop?\"已拨打\":\"已推送\")+\" \")])])])]),e(\"div\",{staticClass:\"swarmsSOP_box\",staticStyle:{display:\"flex\",\"flex-direction\":\"column\"}},[t.isPhoneCallSop?e(\"div\",[t.phoneCallCustomers.length?e(\"div\",{staticClass:\"phone-call-customers\"},t._l(t.phoneCallCustomers,(function(s,a){return e(\"div\",{key:a,staticClass:\"customer-item\"},[e(\"div\",{staticClass:\"customer-info\"},[e(\"div\",{staticClass:\"customer-avatar\"},[s.avatar?e(\"img\",{attrs:{src:s.avatar,alt:\"头像\"}}):e(\"div\",{staticClass:\"default-avatar\"},[t._v(t._s(s.customerName?s.customerName.charAt(0):\"客\"))])]),e(\"div\",{staticClass:\"customer-details\"},[e(\"div\",{staticClass:\"customer-name\"},[t._v(t._s(s.customerName||\"未知客户\"))]),e(\"div\",{staticClass:\"customer-phone\",staticStyle:{\"pointer-events\":\"none\"}},[t._v(t._s(s.customerPhone||\"未设置电话\"))]),e(\"div\",{staticClass:\"customer-status\"},[e(\"span\",{class:t.getCustomerStatusClass(s)},[t._v(t._s(t.getCustomerStatusText(s)))])]),1===t.tabBar&&s.callTime?e(\"div\",{staticClass:\"call-time\"},[t._v(\" 拨打时间：\"+t._s(t.formatCallTime(s.callTime))+\" \")]):t._e()])]),e(\"div\",{staticClass:\"customer-actions\"},[e(\"div\",{staticClass:\"action-buttons\"},[s.customerPhone?e(\"div\",{staticClass:\"phone-call-btn\",on:{click:function(e){return t.makePhoneCallForCustomer(s)}}},[t._v(\" 📞 拨打电话 \")]):e(\"div\",{staticClass:\"phone-call-btn disabled\"},[t._v(\" 📞 无电话号码 \")]),s.externalUserid?e(\"div\",{staticClass:\"voice-call-btn\",on:{click:function(e){return t.makeVoiceCallForCustomer(s)}}},[t._v(\" 🎤 语音通话 \")]):t._e()])])])})),0):e(\"div\",{staticClass:\"phone-call-tip\"},[e(\"div\",{staticClass:\"tip-icon\"},[t._v(\"📞\")]),e(\"div\",{staticClass:\"tip-text\"},[t._v(\"暂无拨打电话任务\")]),e(\"div\",{staticClass:\"tip-desc\"},[t._v(\"当有客户需要电话跟进时，任务会在这里显示\")])])]):e(\"div\",[t.dataList.length?t._l(t.dataList,(function(a,i){return e(\"div\",{key:i,staticClass:\"swarmsSOP_content\"},[e(\"div\",{class:{swarmsSOP_content_top1:1===a.type,swarmsSOP_content_top2:2===a.type,swarmsSOP_content_top3:3===a.type}},[e(\"div\",{staticClass:\"swarmsSOP_content_top_text\",staticStyle:{\"margin-bottom\":\"12px\"}},[t._v(t._s(a.sopName))]),1===a.type&&0===t.tabBar?e(\"div\",{staticClass:\"swarmsSOP_content_top_text\"},[t._v(\" 距推送结束剩余\"+t._s(a.time)+\" \")]):t._e(),2===a.type&&0===t.tabBar?e(\"div\",{staticClass:\"swarmsSOP_content_top_text\"},[t._v(\" 距推送时间已过\"+t._s(a.time)+\" \")]):t._e(),3===a.type&&0===t.tabBar?e(\"div\",{staticClass:\"swarmsSOP_content_top_text\"},[t._v(\"未到推送时间\")]):t._e()]),e(\"div\",{staticClass:\"swarmsSOP_content_title\",on:{click:function(t){a.open=!a.open}}},[e(\"div\",[t._v(\"SOP内容\")]),e(\"div\",{staticClass:\"swarmsSOP_message_content_box_li_right\"},[a.open?t._e():e(\"img\",{attrs:{src:s(\"4e28\"),alt:\"\"}}),a.open?e(\"img\",{staticStyle:{transform:\"rotate(180deg)\"},attrs:{src:s(\"4e28\"),alt:\"\"}}):t._e()])]),a.open?e(\"div\",{staticClass:\"swarmsSOP_content_li\"},[t._l(a.list,(function(s,i){return[e(\"div\",{key:i,staticClass:\"unit\"},[e(\"ShowSendInfo\",{key:i+111,attrs:{obj:s.weQrAttachments}}),0===t.tabBar?e(\"div\",{key:i,staticClass:\"operation\"},[3!==a.type&&1===s.executeState?e(\"span\",{staticStyle:{color:\"#00bf2f\"}},[t._v(\"已发送\")]):t._e(),3!==a.type&&0===s.executeState?e(\"span\",{staticStyle:{color:\"#ed4014\"}},[t._v(\"待发送\")]):t._e(),3===a.type?e(\"span\",[t._v(\"未到推送时间\")]):t._e(),0===s.executeState&&3!==a.type?e(\"van-button\",{attrs:{type:\"info\"},on:{click:function(e){return t.send(s.weQrAttachments,s.executeTargetAttachId)}}},[t._v(\" 发送 \")]):t._e()],1):t._e()],1)]}))],2):t._e()])})):e(\"NoData\")],2)])],1),e(\"Loading\",{attrs:{isLoad:t.isLoad}}),t.showDebugPanel?e(\"div\",{staticClass:\"debug-panel\"},[e(\"div\",{staticClass:\"debug-header\"},[e(\"span\",[t._v(\"调试信息\")]),e(\"div\",{staticClass:\"debug-controls\"},[e(\"button\",{staticClass:\"debug-btn\",on:{click:t.debugDataStatus}},[t._v(\"调试\")]),e(\"button\",{staticClass:\"debug-btn\",on:{click:t.copyDebugLogs}},[t._v(\"复制\")]),e(\"button\",{staticClass:\"debug-btn\",attrs:{title:\"单击清除，双击完全清除\"},on:{click:t.clearDebugLogs,dblclick:t.forceClearDebugLogs}},[t._v(\"清除\")]),e(\"button\",{staticClass:\"debug-btn close-btn\",on:{click:function(e){t.showDebugPanel=!1}}},[t._v(\"关闭\")])])]),e(\"div\",{ref:\"debugContent\",staticClass:\"debug-content\"},t._l(t.debugLogs,(function(s,a){return e(\"div\",{key:a,class:[\"debug-log\",s.type]},[e(\"span\",{staticClass:\"debug-level\"},[t._v(t._s(s.type.toUpperCase()))]),e(\"span\",{staticClass:\"debug-time\"},[t._v(t._s(s.time))]),e(\"span\",{staticClass:\"debug-message\"},[t._v(t._s(s.message))]),s.data?e(\"pre\",{staticClass:\"debug-data\"},[t._v(t._s(\"object\"===typeof s.data?JSON.stringify(s.data,null,2):s.data))]):t._e()])})),0)]):t._e(),e(\"div\",{staticClass:\"debug-toggle\",on:{click:function(e){t.showDebugPanel=!t.showDebugPanel}}},[t._v(\" 🐛 \")])],1)},r=[],n=(s(\"d9e2\"),s(\"14d9\"),s(\"e9f5\"),s(\"910d\"),s(\"f665\"),s(\"7d54\"),s(\"ab43\"),s(\"a732\"),s(\"1419\")),c=s(\"3a5e\"),l=s(\"9815\"),d=s(\"ed08\"),u=s(\"8d34\"),h=function(){var t=this,e=t._self._c;return e(\"div\",[[\"text\"===t.obj.msgType?e(\"div\",{staticClass:\"msg\"},[e(\"div\",{staticClass:\"word\"},[t._v(\" \"+t._s(t.obj.content)+\" \")])]):t._e(),\"image\"===t.obj.msgType?e(\"div\",{staticClass:\"msg\"},[e(\"div\",{staticClass:\"image\"},[e(\"img\",{attrs:{src:t.obj.picUrl}})])]):t._e(),\"link\"!==t.obj.msgType||t.obj.appId?t._e():e(\"div\",{staticClass:\"msg\"},[e(\"div\",{staticClass:\"word-and-image\"},[e(\"div\",[t._v(\" \"+t._s(t.obj.title)+\" \")]),e(\"div\",{staticClass:\"sub-content\"},[e(\"div\",{staticClass:\"digest\"},[t._v(\" \"+t._s(t.obj.linkUrl)+\" \")]),t._m(0)])])]),\"link\"===t.obj.msgType&&t.obj.appId?e(\"div\",{staticClass:\"msg\"},[e(\"div\",{staticClass:\"word-and-image\"},[e(\"div\",[t._v(\" \"+t._s(t.obj.title)+\" \")]),e(\"div\",{staticClass:\"sub-content\"},[e(\"div\",{staticClass:\"digest\"},[t._v(\" \"+t._s(t.obj.linkUrl)+\" \")]),t._m(1)])])]):t._e(),\"video\"===t.obj.msgType?e(\"div\",{staticClass:\"msg\"},[e(\"div\",{staticClass:\"word-and-image\"},[e(\"div\",[t._v(\" \"+t._s(t.obj.title)+\" \")]),e(\"div\",{staticClass:\"sub-content\"},[e(\"div\",{staticClass:\"digest\"},[t._v(\" \"+t._s(t.obj.description)+\" \")]),e(\"div\",{staticStyle:{height:\"70px\",\"flex-shrink\":\"0\"}},[e(\"img\",{staticStyle:{height:\"70px\",width:\"70px\"},attrs:{src:t.obj.picUrl}})])])])]):t._e(),\"file\"===t.obj.msgType?e(\"div\",{staticClass:\"msg\"},[e(\"div\",{staticClass:\"word-and-image\"},[e(\"div\",[t._v(\" \"+t._s(t.obj.title)+\" \")]),e(\"div\",{staticClass:\"sub-content\",staticStyle:{\"min-width\":\"100px\"}},[e(\"div\",{staticClass:\"digest\"},[t._v(\" \"+t._s(t.obj.description)+\" \")]),e(\"div\",[t.obj.linkUrl?e(\"svg-icon\",{staticClass:\"svg\",attrs:{name:t.obj.linkUrl?t.filType(t.obj.linkUrl):\"\"}}):t._e()],1)])])]):t._e(),\"miniprogram\"===t.obj.msgType?e(\"div\",{staticClass:\"msg\"},[e(\"div\",{staticClass:\"miniprogram\"},[e(\"div\",{staticClass:\"mini-header\"},[e(\"img\",{attrs:{src:s(\"a6a3\"),alt:\"\"}}),t._v(\" \"+t._s(t.obj.materialName))]),e(\"img\",{staticClass:\"mini-img\",attrs:{src:t.obj.picUrl}}),t._m(2)])]):t._e()]],2)},g=[function(){var t=this,e=t._self._c;return e(\"div\",{staticStyle:{\"flex-shrink\":\"0\"}},[e(\"img\",{attrs:{src:s(\"a1f1\")}})])},function(){var t=this,e=t._self._c;return e(\"div\",{staticStyle:{height:\"70px\",\"flex-shrink\":\"0\"}},[e(\"img\",{staticStyle:{height:\"70px\",width:\"70px\"},attrs:{src:s(\"a1f1\")}})])},function(){var t=this,e=t._self._c;return e(\"div\",{staticClass:\"mini-footer\"},[e(\"img\",{attrs:{src:s(\"e0de\")}}),t._v(\" 小程序\")])}],m={name:\"show-send-info\",props:{obj:{type:Object,default:()=>{}}},data(){return{}},methods:{filType(t){let e=JSON.parse(JSON.stringify(t));e=e.split(\".\");let s=e[e.length-1];return\"pdf\"===s?\"pdf\":\"doc\"===s||\"docx\"===s?\"word\":\"xls\"===s||\"xlsx\"===s?\"excel\":\"ppt\"===s||\"pptx\"===s||\"pps\"===s||\"pptsx\"===s?\"ppt\":\"\"}}},p=m,f=(s(\"d30f\"),s(\"2877\")),C=Object(f[\"a\"])(p,h,g,!1,null,\"6bb25868\",null),_=C.exports,b=s(\"d800\"),y=s(\"2934\"),S={name:\"customer-sop\",components:{ShowSendInfo:_,Loading:c[\"a\"],NoData:n[\"default\"]},data(){return{isLoad:!1,trackState:\"\",externalUserId:\"\",tabBar:0,form:{avatar:\"\",customerName:\"\",customerType:null,externalUserid:\"\",gender:null,trackState:null,weCustomerSops:[]},dataList:[],showDebugPanel:!1,debugLogs:[],isClearing:!1}},props:{userId:{type:String,default:\"\"},sopType:{type:Number,default:null},sopBaseId:{type:String,default:\"\"},executeTargetAttachId:{type:String,default:\"\"}},watch:{userId:{immediate:!0,handler(t){t?(this.externalUserId=t,this.getData(0)):this.isPhoneCallSop&&this.getData(0)}},sopType:{immediate:!0,handler(t){14!==t||this.userId||this.getData(0)}}},computed:{isPhoneCallSop(){return 14===this.sopType},pageTitle(){return this.isPhoneCallSop?\"拨打电话SOP\":\"客户SOP\"},phoneCallCustomers(){if(!this.isPhoneCallSop||!this.form||!this.form.weCustomerSops)return[];const t=this.form.weCustomerSops.map(t=>{let e=null;t.weCustomerSopContents&&t.weCustomerSopContents.length>0&&(e=String(t.weCustomerSopContents[0].executeTargetAttachId),console.log(`[DEBUG] 客户 ${t.customerName} 的executeTargetAttachId: ${e}, URL参数: ${this.executeTargetAttachId}`));const s={customerName:t.customerName,customerPhone:t.customerPhone,externalUserid:t.externalUserid,avatar:null,sopName:t.sopName,businessType:t.businessType,sopContents:t.weCustomerSopContents||[],sopBaseId:t.sopBaseId,executeTargetId:this.getExecuteTargetId(t),executeTargetAttachId:e,_sopRef:t};return Object.defineProperty(s,\"executeState\",{get(){if(t.weCustomerSopContents&&t.weCustomerSopContents.length>0){const s=t.weCustomerSopContents.find(t=>String(t.executeTargetAttachId)===String(e));return s&&void 0!==s.callStatus?s.callStatus||0:(console.warn(\"[DEBUG] 未找到匹配的时间段内容，executeTargetAttachId: \"+e),0)}return 0},enumerable:!0,configurable:!0}),Object.defineProperty(s,\"callTime\",{get(){if(t.weCustomerSopContents&&t.weCustomerSopContents.length>0){const s=t.weCustomerSopContents.find(t=>String(t.executeTargetAttachId)===String(e));if(s&&s.callTime)return s.callTime}return null},enumerable:!0,configurable:!0}),s});let e;return 0===this.tabBar?e=t.filter(t=>0===t.executeState):(e=t.filter(t=>1===t.executeState),e.sort((t,e)=>{const s=t.callTime?new Date(t.callTime).getTime():0,a=e.callTime?new Date(e.callTime).getTime():0;return a-s})),e},completedCustomersCount(){return this.isPhoneCallSop&&this.form&&this.form.weCustomerSops?this.form.weCustomerSops.filter(t=>1===t.callStatus).length:0}},created(){},mounted(){const t=document.createElement(\"meta\");t.name=\"format-detection\",t.content=\"telephone=no\",document.head.appendChild(t)},methods:{hasPhoneCallSopInDataList(){return Object(u[\"hasPhoneCallSop\"])(this.dataList)},handlePhoneCallClick(t,e){Object(u[\"makePhoneCall\"])(t,e)},async makePhoneCallDirectly(t,e){try{const s=/^1[3-9]\\d{9}$/,a=t.replace(/\\D/g,\"\");if(!s.test(a))return void this.$toast(\"电话号码格式不正确\");this.makePhoneCallByMobile(a,e)}catch(s){console.error(\"拨打电话失败:\",s),this.$toast(\"拨打失败：\"+(s.message||\"未知错误\"))}},makePhoneCallByMobile(t,e){try{const e=/^1[3-9]\\d{9}$/,s=t.replace(/\\D/g,\"\");if(!e.test(s))return void this.$toast(\"电话号码格式不正确\");const a=/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);if(a){const t=\"tel:\"+s,e=document.createElement(\"a\");e.href=t,e.style.display=\"none\",document.body.appendChild(e),e.click(),document.body.removeChild(e),this.$toast(\"正在拨打电话...\")}else this.$toast(\"拨打电话功能仅支持手机设备，请在手机上使用\")}catch(s){console.error(\"手机拨打失败:\",s),this.$toast(\"拨打失败：\"+(s.message||\"未知错误\"))}},getCurrentCustomerPhone(){if(!this.isPhoneCallSop||!this.dataList||0===this.dataList.length)return null;const t=this.dataList[0];return this.getCustomerPhoneFromSop(t)},getCustomerPhoneFromSop(t){if(!t||!t.sopName)return null;const e=t.sopName.split(\" - \");if(e.length<2)return null;const s=e[1];if(this.form&&this.form.weCustomerSops)for(const a of this.form.weCustomerSops){if(a.customerPhone)return a.customerPhone;a.sopName&&a.sopName.includes(s)}return null},makePhoneCallForCurrentCustomer(){const t=this.getCurrentCustomerPhone();t?this.makePhoneCall(t,{customerName:\"当前客户\"}):this.$toast(\"客户未设置电话号码\")},makePhoneCallForSop(t){const e=this.getCustomerPhoneFromSop(t);if(e){const s=t.sopName.split(\" - \")[1]||\"未知客户\";Object(u[\"makePhoneCall\"])(e,{customerName:s})}else this.$toast(\"客户未设置电话号码\")},async makePhoneCallForCustomer(t){if(this.addDebugLog(\"info\",\"开始拨打电话\",t),this.addDebugLog(\"info\",\"executeTargetAttachId值: \"+t.executeTargetAttachId),this.addDebugLog(\"info\",\"executeTargetAttachId类型: \"+typeof t.executeTargetAttachId),!t.customerPhone)return void this.$toast(\"客户未设置电话号码\");const e=/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);if(e)try{await this.recordPhoneCall(t),this.$toast(\"已记录拨打状态\"),this.addDebugLog(\"info\",\"拨打电话成功，重新获取后端状态，executeTargetAttachId: \"+t.executeTargetAttachId),await this.getData(this.tabBar,t.executeTargetAttachId),this.form&&this.form.weCustomerSops&&this.form.weCustomerSops.forEach(e=>{e.externalUserid===t.externalUserid&&this.addDebugLog(\"info\",`重新获取后的数据 - 客户: ${e.customerName}, callStatus: ${e.callStatus}, executeTargetAttachId: ${t.executeTargetAttachId}`)}),this.checkAndSwitchTab(),this.makePhoneCallDirectly(t.customerPhone,t)}catch(s){console.error(\"记录拨打电话失败:\",s),this.$toast(\"记录拨打失败：\"+(s.message||\"未知错误\"))}else this.$toast(\"拨打电话功能仅支持手机设备，请在手机上使用\")},async makeVoiceCallForCustomer(t){if(this.addDebugLog(\"info\",\"开始语音通话\",t),this.addDebugLog(\"info\",\"executeTargetAttachId值: \"+t.executeTargetAttachId),this.addDebugLog(\"info\",\"executeTargetAttachId类型: \"+typeof t.executeTargetAttachId),t.externalUserid)if(window.wx&&window.wx.invoke)try{await this.recordPhoneCall(t,\"wechat_voice\"),this.$toast(\"已记录通话状态\"),this.addDebugLog(\"info\",\"语音通话成功，重新获取后端状态，executeTargetAttachId: \"+t.executeTargetAttachId),await this.getData(this.tabBar,t.executeTargetAttachId),this.form&&this.form.weCustomerSops&&this.form.weCustomerSops.forEach(e=>{e.externalUserid===t.externalUserid&&this.addDebugLog(\"info\",`重新获取后的数据 - 客户: ${e.customerName}, callStatus: ${e.callStatus}, executeTargetAttachId: ${t.executeTargetAttachId}`)}),this.checkAndSwitchTab(),this.openVoiceChatWindow(t)}catch(e){console.error(\"记录语音通话失败:\",e),this.$toast(\"记录通话失败：\"+(e.message||\"未知错误\")),this.addDebugLog(\"error\",\"记录语音通话失败\",e)}else this.$toast(\"请在企业微信中使用语音通话功能\");else this.$toast(\"客户信息不完整，无法发起语音通话\")},openVoiceChatWindow(t){try{window.wx.invoke(\"openEnterpriseChat\",{userIds:sessionStorage.userId||\"\",externalUserIds:t.externalUserid,groupName:\"\",chatId:\"\",success:t=>{this.addDebugLog(\"info\",\"语音通话窗口打开成功\",t),this.$toast(\"已打开聊天窗口，可在其中发起语音通话\")},fail:t=>{this.addDebugLog(\"error\",\"语音通话窗口打开失败\",t),t.errMsg&&t.errMsg.indexOf(\"function not exist\")>-1?this.$toast(\"企业微信版本过低，请升级后使用\"):this.$toast(\"打开聊天窗口失败：\"+(t.errMsg||\"未知错误\"))}})}catch(e){console.error(\"语音通话失败:\",e),this.$toast(\"语音通话失败：\"+(e.message||\"未知错误\")),this.addDebugLog(\"error\",\"语音通话失败\",e)}},getCustomerStatusClass(t){const e=t.executeState;return{\"status-pending\":0===e,\"status-completed\":1===e}},getCustomerStatusText(t){const e=t.executeState;return 0===this.tabBar?0===e?\"待拨打\":\"已拨打\":1===e?\"已拨打\":\"待拨打\"},async recordPhoneCall(t,e=\"mobile\"){const{recordPhoneCall:a}=await s.e(\"chunk-2d0db2ff\").then(s.bind(null,\"6f92\")),i=\"wechat_voice\"===e,o={sopBaseId:t.sopBaseId,executeTargetId:t.executeTargetId,executeTargetAttachId:t.executeTargetAttachId,externalUserid:t.externalUserid,customerName:t.customerName,customerPhone:i?t.customerPhone||\"无\":t.customerPhone,callMethod:e,remark:i?\"通过拨打电话SOP发起（企业微信语音通话）\":\"通过拨打电话SOP发起（手机拨打）\"};if(!o.sopBaseId)throw new Error(\"SOP基础ID不能为空\");if(!o.externalUserid)throw new Error(\"客户ID不能为空\");if(!i&&!o.customerPhone)throw new Error(\"客户电话不能为空\");if(!o.executeTargetAttachId)throw this.addDebugLog(\"error\",\"executeTargetAttachId为空，客户数据\",t),new Error(\"SOP执行目标附件ID不能为空，请检查数据完整性\");this.addDebugLog(\"api\",\"准备调用记录接口\",{url:\"/phone/recordCall\",method:\"POST\",requestData:o});const r=/^1[3-9]\\d{9}$/;if(!r.test(o.customerPhone.replace(/\\D/g,\"\")))throw new Error(\"客户电话号码格式不正确\");o.customerName&&\"\"!==o.customerName.trim()||(o.customerName=\"未知客户\");const n=new Promise((t,e)=>{setTimeout(()=>e(new Error(\"请求超时，请检查网络连接\")),1e4)});try{const t=await Promise.race([a(o),n]);if(this.addDebugLog(\"api\",\"记录接口响应\",{url:\"/phone/recordCall\",status:t.code,message:t.msg,data:t.data,success:200===t.code}),200===t.code)return t.data;throw new Error(t.msg||\"记录拨打电话失败\")}catch(c){throw this.addDebugLog(\"api\",\"记录接口调用失败\",{url:\"/phone/recordCall\",error:c.message,requestData:o}),c}},addDebugLog(t,e,s=null){if(this.isClearing&&!e.includes(\"调试日志已手动清除\"))return;const a={type:t,message:e,data:s?JSON.stringify(s,null,2):null,time:(new Date).toLocaleTimeString()};this.debugLogs.push(a),this.debugLogs.length>100&&this.debugLogs.shift(),this.$nextTick(()=>{this.$refs.debugContent&&(this.$refs.debugContent.scrollTop=this.$refs.debugContent.scrollHeight)})},copyDebugLogs(){try{const t=this.debugLogs.map(t=>{let e=`[${t.time}] ${t.type.toUpperCase()}: ${t.message}`;return t.data&&(e+=\"\\n\"+t.data),e}).join(\"\\n\\n\");navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(t).then(()=>{this.$toast(\"调试日志已复制到剪贴板\")}).catch(()=>{this.fallbackCopyText(t)}):this.fallbackCopyText(t)}catch(t){this.$toast(\"复制失败：\"+t.message)}},fallbackCopyText(t){try{const e=document.createElement(\"textarea\");e.value=t,e.style.position=\"fixed\",e.style.left=\"-999999px\",e.style.top=\"-999999px\",document.body.appendChild(e),e.focus(),e.select();const s=document.execCommand(\"copy\");document.body.removeChild(e),s?this.$toast(\"调试日志已复制到剪贴板\"):this.$toast(\"复制失败，请手动复制\")}catch(e){this.$toast(\"复制失败：\"+e.message)}},clearDebugLogs(){const t=this.debugLogs.length;this.isClearing=!0,this.debugLogs.splice(0,this.debugLogs.length),this.$nextTick(()=>{this.$refs.debugContent&&(this.$refs.debugContent.scrollTop=0),setTimeout(()=>{this.isClearing=!1,this.addDebugLog(\"success\",`调试日志已手动清除，共清除 ${t} 条记录`)},100)}),this.$toast(\"调试日志已清除\")},forceClearDebugLogs(){this.debugLogs.splice(0,this.debugLogs.length),this.$nextTick(()=>{this.$refs.debugContent&&(this.$refs.debugContent.scrollTop=0)}),this.$toast(\"调试日志已完全清除\")},debugDataStatus(){if(this.addDebugLog(\"info\",\"=== 手动调试数据状态 ===\"),this.isPhoneCallSop){const t=this.phoneCallCustomers;this.addDebugLog(\"info\",\"拨打电话SOP客户列表，总记录数: \"+t.length),t.forEach(t=>{this.addDebugLog(\"info\",`客户: ${t.customerName}, executeTargetAttachId: ${t.executeTargetAttachId}, executeState: ${t.executeState}, _sopRef.callStatus: ${t._sopRef.callStatus}, 拨打状态: ${1===t.executeState?\"已拨打\":\"待拨打\"}`)});const e=t.filter(t=>0===t.executeState),s=t.filter(t=>1===t.executeState);this.addDebugLog(\"info\",`过滤结果 - 待拨打: ${e.length}个, 已拨打: ${s.length}个, 当前标签页: ${0===this.tabBar?\"待拨打\":\"已拨打\"}`)}else this.addDebugLog(\"info\",\"当前不是拨打电话SOP页面\");this.addDebugLog(\"info\",\"=== 调试完成 ===\")},checkAndSwitchTab(){this.isPhoneCallSop&&this.$nextTick(()=>{const t=this.phoneCallCustomers.filter(t=>0===t.executeState),e=this.phoneCallCustomers.filter(t=>1===t.executeState);this.addDebugLog(\"info\",`标签页切换检查 - 待拨打: ${t.length}个, 已拨打: ${e.length}个, 当前标签页: ${0===this.tabBar?\"待拨打\":\"已拨打\"}`),0===this.tabBar&&0===t.length&&e.length>0&&(this.addDebugLog(\"success\",\"自动切换到已拨打标签页\"),this.tabBar=1,this.$toast(\"已自动切换到已拨打列表\"))})},getExecuteTargetId(t){if(t.weCustomerSopContents&&t.weCustomerSopContents.length>0){const e=t.weCustomerSopContents[0];if(e&&e.executeTargetId)return e.executeTargetId}return null},filType(t){let e=JSON.parse(JSON.stringify(t));e=e.split(\".\");let s=e[e.length-1];return\"pdf\"===s?window.sysConfig.DEFAULT_H5_PDF:[\"doc\",\"docx\"].includes(s)?window.sysConfig.DEFAULT_H5_WORDE:[\"ppt\",\"pptx\",\"pps\",\"pptsx\"].includes(s)?window.sysConfig.DEFAULT_H5_PPT:window.sysConfig.DEFAULT_H5_PIC},getStage(){Object(y[\"e\"])().then(t=>{var e;const s=null===(e=t.data)||void 0===e?void 0:e.map(t=>({text:t.stageKey,value:t.stageVal}));null===s||void 0===s||s.some(t=>t.value==this.form.trackState&&(this.trackState=t.text))})},setChange(t){this.tabBar=t,this.getData(t)},send(t,e){this.$toast.loading({message:\"正在发送...\",duration:0,forbidClick:!0});let s=this;wx.invoke(\"getContext\",{},(async function(a){if(\"getContext:ok\"==a.err_msg){let a={};try{switch(t.msgType){case\"text\":default:a.text={content:t.content},a.msgtype=t.msgType;break;case\"image\":let e={url:t.picUrl,type:t.msgType,name:t.materialName};try{let i=await Object(b[\"f\"])(e);if(!i.data)return void s.$toast(\"获取素材id失败\");s.$set(a,t.msgType,{mediaid:i.data.mediaId}),a.msgtype=t.msgType}catch(i){return void s.$toast.clear()}break;case\"video\":case\"file\":let o=window.document.location.origin+window.sysConfig.BASE_URL+\"#/metrialDetail?mediaType=\"+t.msgType+\"&materialUrl=\"+t.linkUrl;a.news={link:o,title:t.title?t.title:\"\",desc:t.description?t.description:\"\",imgUrl:t.picUrl||s.filType(t.linkUrl)||window.sysConfig.DEFAULT_H5_PIC},a.msgtype=\"news\";break;case\"link\":a.news={link:t.linkUrl,title:t.title?t.title:\"\",desc:t.description?t.description:\"\",imgUrl:window.sysConfig.DEFAULT_H5_PIC},a.msgtype=\"news\";break;case\"miniprogram\":a.miniprogram={appid:t.appId,title:t.title,imgUrl:t.picUrl,page:t.linkUrl},a.msgtype=t.msgType;break}}catch(o){s.$dialog({message:\"err\"+JSON.stringify(o)})}wx.invoke(\"sendChatMessage\",a,(function(t){\"sendChatMessage:ok\"==t.err_msg&&s.setSuccessFn(e),\"sendChatMessage:cancel,sendChatMessage:ok\".indexOf(t.err_msg)<0&&s.$dialog({message:\"发送失败：\"+JSON.stringify(t)})})),s.$toast.clear()}else s.$toast.clear(),s.$dialog({message:\"进入失败：\"+JSON.stringify(a)})}))},setSuccessFn(t){Object(l[\"d\"])(t).then(()=>{this.getData(0)})},async getData(t,e=null){if(this.isLoad=!0,this.isPhoneCallSop&&!this.externalUserId){const i={executeSubState:t,businessType:7};i.sopBaseId=this.sopBaseId;const o=e||this.executeTargetAttachId;i.executeTargetAttachId=o,this.addDebugLog(\"info\",`重新获取数据 - sopBaseId: ${this.sopBaseId}, executeTargetAttachId: ${o}${e?\" (指定时间段)\":\" (默认时间段)\"}`),this.addDebugLog(\"api\",\"准备调用查询接口\",{url:\"/sop/findCustomerSopContent\",method:\"GET\",requestParams:i});try{var s;const t=await Object(l[\"b\"])(i);this.addDebugLog(\"api\",\"查询接口响应\",{url:\"/sop/findCustomerSopContent\",status:t.code,message:t.msg,dataCount:(null===(s=t.data)||void 0===s||null===(s=s.weCustomerSops)||void 0===s?void 0:s.length)||0,success:200===t.code}),200===t.code?this.form=t.data||{customerName:\"拨打电话SOP\",weCustomerSops:[]}:this.form={customerName:\"拨打电话SOP\",weCustomerSops:[]}}catch(a){this.addDebugLog(\"api\",\"查询接口调用失败\",{url:\"/sop/findCustomerSopContent\",error:a.message,requestParams:i}),this.addDebugLog(\"error\",\"API调用异常\",a),this.form={customerName:\"拨打电话SOP\",weCustomerSops:[]}}finally{this.isLoad=!1}}else Object(l[\"b\"])({targetId:this.externalUserId,executeSubState:t}).then(t=>{if(200===t.code){this.form=t.data,this.getStage();let e=this.form.weCustomerSops;e&&e.length?this.resetData(e):this.dataList=[]}this.isLoad=!1})},resetData(t){this.dataList=[],t.forEach(t=>{let e=this.sorted(t.weCustomerSopContents,\"pushStartTime\"),s=Object.keys(e),a=[];s.forEach(s=>{a.push({sopBaseId:t.sopBaseId,sopName:t.sopName,businessType:t.businessType,open:!1,list:e[s]})}),this.dataList.push(...a)}),this.setStateData()},setStateData(){this.dataList.forEach(t=>{let e=Object(d[\"a\"])(t.list[0].pushStartTime,t.list[0].pushEndTime);\"before\"===e?t.type=3:\"after\"===e?(t.type=2,t.time=this.computeTime(t.list[0].pushEndTime)):(t.time=this.computeTime(t.list[0].pushEndTime),t.type=1)})},computeTime(t){let e=new Date(t.replace(/-/g,\"/\")),s=new Date,a=e.getTime()-s.getTime(),i=a%864e5,o=Math.abs(parseInt(i/36e5)),r=i%36e5,n=Math.abs(parseInt(r/6e4)),c=r%6e4,l=Math.abs(parseInt(c/1e3));return`${o}时${n}分${l}左右`},sorted(t,e){let s={};return t.forEach((function(t){let a=t[e];s[a]=s[a]||[],s[a].push(t)})),s},formatCallTime(t){if(!t)return\"\";try{const e=new Date(t),s=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,\"0\"),i=String(e.getDate()).padStart(2,\"0\"),o=String(e.getHours()).padStart(2,\"0\"),r=String(e.getMinutes()).padStart(2,\"0\");return`${s}-${a}-${i} ${o}:${r}`}catch(e){return console.error(\"格式化拨打时间失败:\",e),t}}}},v=S,w=(s(\"bab2\"),Object(f[\"a\"])(v,o,r,!1,null,\"27c0f2fc\",null)),T=w.exports,x=function(){var t=this,e=t._self._c;return e(\"div\",{staticClass:\"swarmsSOP\"},[e(\"div\",[e(\"van-sticky\",[e(\"div\",{staticStyle:{background:\"#fafafa\"}},[e(\"div\",{staticClass:\"swarmsSOP_message\"},[e(\"div\",{staticClass:\"swarmsSOP_message_top\"},[e(\"div\",{staticClass:\"swarmsSOP_message_top_left\"},[e(\"div\",{staticClass:\"swarmsSOP_message_top_left_info\"},[e(\"div\",[t._v(\" \"+t._s(t.form.groupName)+\" \")]),e(\"div\",{staticStyle:{\"margin-top\":\"10px\"}},[t._v(\"创建时间：\"+t._s(t.form.addTime))])])])])]),e(\"div\",{staticClass:\"swarmsSOP_tabBar\"},[e(\"div\",{class:0==t.tabBar?\"swarmsSOP_tabBar_li1\":\"swarmsSOP_tabBar_li\",on:{click:function(e){return t.setChange(0)}}},[t._v(\" 待推送 \")]),e(\"div\",{class:1==t.tabBar?\"swarmsSOP_tabBar_li1\":\"swarmsSOP_tabBar_li\",on:{click:function(e){return t.setChange(1)}}},[t._v(\" 已推送 \")])])])]),e(\"div\",{staticClass:\"swarmsSOP_box\",staticStyle:{display:\"flex\",\"flex-direction\":\"column\",\"align-items\":\"center\"}},[t.dataList.length?t._l(t.dataList,(function(a,i){return e(\"div\",{key:i,staticClass:\"swarmsSOP_content\"},[e(\"div\",{class:{swarmsSOP_content_top1:1===a.type,swarmsSOP_content_top2:2===a.type,swarmsSOP_content_top3:3===a.type}},[e(\"div\",{staticClass:\"swarmsSOP_content_top_text\",staticStyle:{\"margin-bottom\":\"12px\"}},[t._v(t._s(a.sopName))]),1===a.type&&0===t.tabBar?e(\"div\",{staticClass:\"swarmsSOP_content_top_text\"},[t._v(\" 距推送结束剩余\"+t._s(a.time)+\" \")]):t._e(),2===a.type&&0===t.tabBar?e(\"div\",{staticClass:\"swarmsSOP_content_top_text\"},[t._v(\" 距推送时间已过\"+t._s(a.time)+\" \")]):t._e(),3===a.type&&0===t.tabBar?e(\"div\",{staticClass:\"swarmsSOP_content_top_text\"},[t._v(\"未到推送时间\")]):t._e()]),e(\"div\",{staticClass:\"swarmsSOP_content_title\",on:{click:function(t){a.open=!a.open}}},[e(\"div\",[t._v(\" SOP内容 \")]),e(\"div\",{staticClass:\"swarmsSOP_message_content_box_li_right\"},[a.open?t._e():e(\"img\",{attrs:{src:s(\"4e28\"),alt:\"\"}}),a.open?e(\"img\",{staticStyle:{transform:\"rotate(180deg)\"},attrs:{src:s(\"4e28\"),alt:\"\"}}):t._e()])]),a.open?e(\"div\",{staticClass:\"swarmsSOP_content_li\"},[t._l(a.list,(function(s,i){return[e(\"div\",{key:i,staticClass:\"unit\"},[e(\"ShowSendInfo\",{key:i+111,attrs:{obj:s.weQrAttachments}}),0===t.tabBar?e(\"div\",{key:i,staticClass:\"operation\"},[3!==a.type&&1===s.executeState?e(\"span\",{staticStyle:{color:\"#00bf2f\"}},[t._v(\"已发送\")]):t._e(),3!==a.type&&0===s.executeState?e(\"span\",{staticStyle:{color:\"#ed4014\"}},[t._v(\"待发送\")]):t._e(),3===a.type?e(\"span\",[t._v(\"未到推送时间\")]):t._e(),0===s.executeState?e(\"van-button\",{attrs:{type:\"info\"},on:{click:function(e){return t.send(s.weQrAttachments,s.executeTargetAttachId)}}},[t._v(\" 发送 \")]):t._e()],1):t._e()],1)]}))],2):t._e()])})):e(\"NoData\")],2)],1),e(\"Loading\",{attrs:{isLoad:t.isLoad}})],1)},P=[],I={name:\"group-sop\",components:{ShowSendInfo:_,Loading:c[\"a\"],NoData:n[\"default\"]},data(){return{isLoad:!1,externalUserId:\"\",tabBar:0,form:{addTime:null,chatId:null,groupName:null,weGroupSops:[]},dataList:[]}},props:{chatId:{type:String,default:\"\"},sopType:{type:Number,default:null}},watch:{chatId:{immediate:!0,handler(t){t&&(this.externalUserId=t,this.getData(0))}}},created(){},methods:{filType(t){let e=JSON.parse(JSON.stringify(t));e=e.split(\".\");let s=e[e.length-1];return\"pdf\"===s?window.sysConfig.DEFAULT_H5_PDF:[\"doc\",\"docx\"].includes(s)?window.sysConfig.DEFAULT_H5_WORDE:[\"ppt\",\"pptx\",\"pps\",\"pptsx\"].includes(s)?window.sysConfig.DEFAULT_H5_PPT:window.sysConfig.DEFAULT_H5_PIC},setChange(t){this.tabBar=t,this.getData(t)},send(t,e){this.$toast.loading({message:\"正在发送...\",duration:0,forbidClick:!0});let s=this;wx.invoke(\"getContext\",{},(async function(a){if(\"getContext:ok\"==a.err_msg){let a={};try{switch(t.msgType){case\"text\":default:a.text={content:t.content},a.msgtype=t.msgType;break;case\"image\":let e={url:t.picUrl,type:t.msgType,name:t.materialName};try{let i=await Object(b[\"f\"])(e);if(!i.data)return void s.$toast(\"获取素材id失败\");s.$set(a,t.msgType,{mediaid:i.data.mediaId}),a.msgtype=t.msgType}catch(i){return void s.$toast.clear()}break;case\"video\":case\"file\":let o=window.document.location.origin+window.sysConfig.BASE_URL+\"#/metrialDetail?mediaType=\"+t.msgType+\"&materialUrl=\"+t.linkUrl;a.news={link:o,title:t.title?t.title:\"\",desc:t.description?t.description:\"\",imgUrl:t.picUrl||s.filType(t.linkUrl)||window.sysConfig.DEFAULT_H5_PIC},a.msgtype=\"news\";break;case\"link\":a.news={link:t.linkUrl,title:t.title?t.title:\"\",desc:t.description?t.description:\"\",imgUrl:window.sysConfig.DEFAULT_H5_PIC},a.msgtype=\"news\";break;case\"miniprogram\":a.miniprogram={appid:t.appId,title:t.title,imgUrl:t.picUrl,page:t.linkUrl},a.msgtype=t.msgType;break}}catch(o){s.$dialog({message:\"err\"+JSON.stringify(o)})}wx.invoke(\"sendChatMessage\",a,(function(t){\"sendChatMessage:ok\"==t.err_msg&&s.setSuccessFn(e),\"sendChatMessage:cancel,sendChatMessage:ok\".indexOf(t.err_msg)<0&&s.$dialog({message:\"发送失败：\"+JSON.stringify(t)})})),s.$toast.clear()}else s.$toast.clear(),s.$dialog({message:\"进入失败：\"+JSON.stringify(a)})}))},setSuccessFn(t){Object(l[\"d\"])(t).then(t=>{this.getData(0)})},getData(t){this.isLoad=!0,Object(l[\"c\"])({chatId:this.externalUserId,executeSubState:t}).then(t=>{if(200===t.code){this.form=t.data;let e=this.form.weGroupSops;e&&e.length?this.resetData(e):this.dataList=[]}this.isLoad=!1})},resetData(t){this.dataList=[],t.forEach(t=>{let e=this.sorted(t.weGroupSopContents,\"pushStartTime\"),s=Object.keys(e),a=[];s.forEach(s=>{a.push({sopBaseId:t.sopBaseId,sopName:t.sopName,open:!1,list:e[s]})}),this.dataList.push(...a)}),this.setStateData()},setStateData(){this.dataList.forEach(t=>{let e=Object(d[\"a\"])(t.list[0].pushStartTime,t.list[0].pushEndTime);\"before\"===e?t.type=3:\"after\"===e?(t.type=2,t.time=this.computeTime(t.list[0].pushEndTime)):(t.time=this.computeTime(t.list[0].pushEndTime),t.type=1)}),this.$forceUpdate()},computeTime(t){let e=new Date(t.replace(/-/g,\"/\")),s=new Date,a=e.getTime()-s.getTime(),i=a%864e5,o=Math.abs(parseInt(i/36e5)),r=i%36e5,n=Math.abs(parseInt(r/6e4)),c=r%6e4,l=Math.abs(parseInt(c/1e3));return`${o}时${n}分${l}左右`},sorted(t,e){let s={};return t.forEach((function(t){let a=t[e];s[a]=s[a]||[],s[a].push(t)})),s}}},O=I,k=(s(\"6bc2\"),Object(f[\"a\"])(O,x,P,!1,null,\"7c2c590a\",null)),A=k.exports,D={name:\"sop\",components:{customer:T,group:A},data(){let t=null;try{if(this.$route&&this.$route.query&&this.$route.query.type)t=parseInt(this.$route.query.type);else{const e=window.location.hash,s=e.match(/[?&]type=(\\d+)/);s&&(t=parseInt(s[1]))}}catch(e){console.log(\"[SOP_DEBUG] 获取初始sopType失败:\",e),t=null}return console.log(\"[SOP_DEBUG] data()初始化 - initialSopType:\",t),{currentType:\"customer\",externalUserId:\"\",chatId:\"\",sopType:t,sopBaseId:\"\",executeTargetAttachId:\"\",showDebugInfo:!1,currentUrl:window.location.href}},created(){null===this.sopType&&this.$route.query.type&&(this.sopType=parseInt(this.$route.query.type),console.log(\"[SOP_DEBUG] created()中重新获取sopType:\",this.sopType)),this.updatePageTitle(),this.init()},computed:{pageTitle(){return 14===this.sopType?\"拨打电话SOP\":9===this.sopType?\"客户SOP\":10===this.sopType?\"客群SOP\":\"客户/客群SOP\"}},methods:{toggleDebugInfo(){this.showDebugInfo=!this.showDebugInfo,this.currentUrl=window.location.href},updatePageTitle(){14===this.sopType?document.title=\"拨打电话SOP\":9===this.sopType?document.title=\"客户SOP\":10===this.sopType?document.title=\"客群SOP\":document.title=\"客户/客群SOP\"},init(){if(this.$route.query.corpId&&this.$route.query.agentId)return console.log(\"[SOP_DEBUG] 所有URL参数:\",this.$route.query),this.$route.query.sopBaseId?(this.sopBaseId=this.$route.query.sopBaseId,console.log(\"[SOP_DEBUG] 获取到sopBaseId参数:\",this.sopBaseId)):console.warn(\"[SOP_DEBUG] 未获取到sopBaseId参数，可能导致数据混合显示\"),this.$route.query.executeTargetAttachId?(this.executeTargetAttachId=this.$route.query.executeTargetAttachId,console.log(\"[SOP_DEBUG] 获取到executeTargetAttachId参数:\",this.executeTargetAttachId)):console.warn(\"[SOP_DEBUG] 未获取到executeTargetAttachId参数，将返回所有时间段数据\"),14===this.sopType?(this.currentType=\"customer\",this.externalUserId=\"\"):10===this.sopType?(this.currentType=\"group\",this.$route.query.chatId&&(this.chatId=this.$route.query.chatId)):(this.currentType=\"customer\",this.$route.query.externalUserId&&(this.externalUserId=this.$route.query.externalUserId),console.log(\"[PHONE_CALL_SOP_DEBUG] 设置完成 - currentType:\",this.currentType,\"externalUserId:\",this.externalUserId)),void console.log(\"[PHONE_CALL_SOP_DEBUG] 企微通知跳转处理完成，直接返回\");wx.invoke(\"getContext\",{},t=>{if(\"getContext:ok\"==t.err_msg){let e=t.entry;if(![\"single_chat_tools\",\"group_chat_tools\"].includes(e))return void this.$toast(\"入口错误：\"+e);\"group_chat_tools\"===e?(this.currentType=\"group\",wx.invoke(\"getCurExternalChat\",{},t=>{\"getCurExternalChat:ok\"==t.err_msg?this.chatId=t.chatId:this.$dialog({message:\"进入失败：\"+JSON.stringify(t)}),this.$toast.clear()})):wx.invoke(\"getCurExternalContact\",{},t=>{\"getCurExternalContact:ok\"==t.err_msg?this.externalUserId=t.userId:this.$dialog({message:\"进入失败：\"+JSON.stringify(t)}),this.$toast.clear()})}else this.$toast.clear(),this.$dialog({message:\"进入失败：\"+JSON.stringify(t)})})}}},U=D,L=(s(\"f5d6\"),Object(f[\"a\"])(U,a,i,!1,null,\"40d1b6d8\",null));e[\"default\"]=L.exports},f5d6:function(t,e,s){\"use strict\";s(\"6fc5\")}}]);", "extractedComments": []}